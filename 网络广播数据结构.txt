好的，根据你对 Detect_BaseService.py 的修改，网络广播的检测结果数据结构现在如下：

```json
{
    "status": "data_updated",
    "data": {
        "timestamp": "<timestamp_value>",
        "fps": "<fps_value>",
        "process_time_ms": "<process_time_ms_value>",
        "objects": [
            {
                "track_number": "<track_id_or_null>",
                "class_id": "<class_id_int>",
                "class_name": "<class_name_string>",
                "center_abs_pixel": [<center_x_float>, <center_y_float>],
                "center_normalized_prop": [<norm_x_float_or_null>, <norm_y_float_or_null>],
                "confidence": "<confidence_float>",
                "bounding_box_xyxy_abs": [<x1_abs_float>, <y1_abs_float>, <x2_abs_float>, <y2_abs_float>],
                "bounding_box_xyxy_normalized_prop": [
                    <norm_x1_float_or_null>,
                    <norm_y1_float_or_null>,
                    <norm_x2_float_or_null>,
                    <norm_y2_float_or_null>
                ]
            }
            // ... 更多检测到的物体
        ]
    }
}
```

**关键字段说明：**

*   `status`: 字符串，通常为 `"data_updated"`。
*   `data`: 对象，包含实际的检测数据。
    *   `timestamp`: 数字，检测结果生成时的时间戳。
    *   `fps`: 数字，当前的检测帧率。
    *   `process_time_ms`: 数字，处理单帧所需的时间（毫秒）。
    *   `objects`: 数组，每个元素代表一个检测到的物体。
        *   `track_number`: 数字或 `null`，物体的跟踪ID（如果启用了跟踪器且成功跟踪）。
        *   `class_id`: 整数，物体的类别ID。
        *   `class_name`: 字符串，物体的类别名称。
        *   `center_abs_pixel`: 数组 `[数字, 数字]`，物体边界框中心点的绝对像素坐标 `[x, y]`。
        *   `center_normalized_prop`: 数组 `[数字或null, 数字或null]`，物体中心点在ROI内的归一化比例坐标 `[norm_x, norm_y]` (基于透视变换)。如果无法计算，则为 `[null, null]`。
        *   `confidence`: 数字，检测到该物体的置信度。
        *   `bounding_box_xyxy_abs`: 数组 `[数字, 数字, 数字, 数字]`，物体边界框的绝对像素坐标 `[x_min, y_min, x_max, y_max]`。**（**
        *   `bounding_box_xyxy_normalized_prop`: 数组 `[数字或null, 数字或null, 数字或null, 数字或null]`，物体边界框左上角和右下角在ROI内基于透视变换的归一化比例坐标 `[norm_x1, norm_y1, norm_x2, norm_y2]`。如果透视变换失败，对应坐标可能为 `null`。

客户端程序在解析此数据时，应主要关注 `bounding_box_xyxy_normalized_prop` 来获取归一化的边界框坐标。