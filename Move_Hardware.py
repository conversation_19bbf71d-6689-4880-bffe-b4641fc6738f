# 硬件控制模块：包含华控继电器控制和离散输入功能

import yaml
import time
import binascii
from Move_Utils import open_serial, close_serial, crc16, load_motor_config
from Move_Utils import serial_lock as global_serial_lock, trace_lock # 导入全局锁和调试函数
import traceback # 导入 traceback 模块

# ==================== 华控继电器控制功能 ====================

def relay_control(ser, relay_number, state, slave_addr, serial_lock_obj=None):
    """
    控制华控继电器
    
    Args:
        ser: 串口对象
        relay_number: 继电器编号(0表示DO1, 1表示DO2)
        state: 继电器状态(1=开启, 0=关闭)
        slave_addr: 继电器从站地址
        serial_lock_obj: 外部传入的锁对象，为None则使用全局锁
    """
    function_code = 0x05  # 写单个线圈
    # 华控继电器线圈地址：0为第1个继电器，1为第2个继电器
    coil_address = relay_number  # 对应DO1和DO2
    on_value = 0xFF00  # 开启值
    off_value = 0x0000  # 关闭值
    
    value = on_value if state == 1 else off_value
    
    # 构造数据帧
    data = bytes([
        0x00, coil_address,  # 线圈地址
        (value >> 8) & 0xFF, value & 0xFF  # 开/关值
    ])
    
    request_core = bytes([slave_addr, function_code]) + data # Renamed to avoid conflict
    request = request_core + crc16(request_core)
    
    current_lock = serial_lock_obj if serial_lock_obj else global_serial_lock
    response_bytes = None

    trace_lock(f"Attempting to acquire for relay {relay_number+1} {'ON' if state==1 else 'OFF'}", current_lock)
    with current_lock:
        trace_lock(f"Acquired for relay {relay_number+1}", current_lock)
        try:
            # print(f"控制继电器{relay_number + 1}({'开启' if state == 1 else '关闭'}), 地址:{slave_addr}")
            # print(f"发送数据: {' '.join([f'{b:02X}' for b in request])}")
            if ser.in_waiting > 0:
                ser.read(ser.in_waiting)
            ser.write(request)
            time.sleep(0.02) 
            if ser.in_waiting > 0:
                response_bytes = ser.read(ser.in_waiting)
        finally:
            trace_lock(f"Released for relay {relay_number+1}", current_lock)
            
    # if response_bytes:
        # print(f"收到响应: {' '.join([f'{b:02X}' for b in response_bytes])}")
    # else:
        # print("未收到响应")

# ==================== 华控离散输入功能 ====================

def create_read_discrete_input_request(slave_addr, register_addr, register_count):
    """
    创建读取离散输入的Modbus请求
    
    Args:
        slave_addr: 从站地址
        register_addr: 寄存器起始地址
        register_count: 要读取的寄存器个数
    
    Returns:
        Modbus请求数据包
    """
    # 组装请求数据
    data = bytes([
        slave_addr,          # 从站地址
        0x04,                # 功能码04 - 读取离散输入
        register_addr >> 8,  # 寄存器地址高字节
        register_addr & 0xFF,# 寄存器地址低字节
        register_count >> 8, # 寄存器数量高字节
        register_count & 0xFF # 寄存器数量低字节
    ])
    crc_val = crc16(data) # Renamed
    return data + crc_val


def parse_read_discrete_input_response(response):
    """
    解析读取离散输入的响应
    
    Args:
        response: Modbus响应数据包
    
    Returns:
        解析出的数据值，如果响应无效则返回None
    """
    # 检查响应长度是否足够
    if len(response) < 5:
        print("响应数据长度不足")
        return None
    if response[1] != 0x04:
        print(f"功能码错误: {response[1]}")
        return None
    byte_count = response[2]
    if len(response) < 3 + byte_count + 2:
        print("响应数据不完整")
        return None
    data_payload = response[3:3+byte_count] # Renamed
    expected_crc = crc16(response[:-2])
    received_crc = response[-2:]
    if expected_crc != received_crc:
        print(f"CRC校验错误: 期望={binascii.hexlify(expected_crc)}, 接收={binascii.hexlify(received_crc)}")
        return None
    if byte_count >= 2:
        return (data_payload[0] << 8) | data_payload[1]
    elif byte_count == 1:
        return data_payload[0]
    return 0


def read_discrete_input_main(): # For standalone testing
    config = load_motor_config()
    if not config: return # Ensure config loaded
    serial_config = config.get('serial')
    x1_config = config.get('X1') # Gripper config
    if not serial_config or not x1_config:
        print("Serial or X1 configuration missing.")
        return
        
    slave_addr = x1_config['slave_addr']
    
    ser = open_serial(serial_config)
    if not ser: return
    
    try:
        request = create_read_discrete_input_request(slave_addr, 0, 1)
        value = None
        
        trace_lock("Attempting to acquire (standalone discrete_input)", global_serial_lock)
        with global_serial_lock:
            trace_lock("Acquired (standalone discrete_input)", global_serial_lock)
            try:
                if ser.in_waiting > 0: ser.read(ser.in_waiting)
                ser.write(request)
                time.sleep(0.05)
                response_bytes = ser.read(ser.in_waiting)
            finally:
                trace_lock("Released (standalone discrete_input)", global_serial_lock)
        
        # print(f"接收: {binascii.hexlify(response_bytes).decode() if response_bytes else '无响应'}")
        value = parse_read_discrete_input_response(response_bytes)
        
        if value is not None:
            x1_state = value & 0x01
            print("X1(爪子) 打开" if x1_state == 0 else "X1(爪子) 闭合") # Clarified X1 is gripper
        else:
            print("无法获取X1(爪子)状态")
            
    finally:
        close_serial(ser)

# ==================== 爪子状态检查功能 (Moved from Move_Main.py) ====================

def check_gripper_status(ser, config=None, serial_lock_obj=None):
    """检查爪子状态，判断是否抓取到物体

    Args:
        ser: 串口对象 (必须提供)
        config: 配置对象，如果为None则从文件加载 (将使用Move_Utils.load_motor_config的默认路径)
        serial_lock_obj: 外部传入的锁对象, 为None则使用本模块导入的全局锁 (global_serial_lock from Move_Utils)

    Returns:
        bool: True表示闭合(检测到物体)，False表示打开(未检测到物体)
    """
    if not ser or not ser.is_open:
        print(f"错误: check_gripper_status 串口未打开或不可用 (ser: {ser})")
        # 在关键操作前检查串口状态，如果不可用则抛出异常或返回错误
        raise ConnectionError("check_gripper_status: 串口未打开或不可用")

    current_config = config if config else load_motor_config() # 使用Move_Utils中load_motor_config的默认配置文件路径
    if not current_config:
        raise ValueError("check_gripper_status: 无法加载配置文件")

    # 确定使用的锁
    lock_to_use = serial_lock_obj if serial_lock_obj else global_serial_lock

    try:
        request = create_read_discrete_input_request(
            current_config['X1']['slave_addr'], 0, 1 # X1离散输入通常在地址0
        )
        
        response_value = None
        response_bytes_for_debug = b'' # 用于在解析失败时记录原始字节

        trace_lock("Attempting to acquire for check_gripper_status", lock_to_use, func_name="check_gripper_status")
        with lock_to_use: # 在锁内执行串口操作
            trace_lock("Acquired for check_gripper_status", lock_to_use, func_name="check_gripper_status")
            try:
                if hasattr(ser, 'reset_input_buffer'):
                    ser.reset_input_buffer() # 清理输入缓冲区
                elif ser.in_waiting > 0:
                    ser.read(ser.in_waiting)

                ser.write(request)
                
                # 等待设备准备返回，复用 temp_height_time 或一个专用的小延迟
                delay = current_config.get('X1', {}).get('read_delay', 
                                                        current_config.get('temp_height', {}).get('temp_height_time', 0.05))
                time.sleep(delay)

                response_bytes = b''
                # 尝试读取响应，直到超时或读到数据
                read_deadline = time.time() + ser.timeout # 使用串口本身的超时设置
                while time.time() < read_deadline:
                    if ser.in_waiting > 0:
                        response_bytes = ser.read(ser.in_waiting)
                        response_bytes_for_debug = response_bytes # 保存用于可能的调试打印
                        break
                    time.sleep(0.01) # 短暂休眠，避免CPU空转

                if not response_bytes:
                    print(f"警告: check_gripper_status 未收到串口响应 (超时: {ser.timeout}s)")
                    return False # 或抛出 TimeoutError
            finally:
                trace_lock("Released for check_gripper_status", lock_to_use, func_name="check_gripper_status")
        
        if response_bytes: # 仅当收到数据时才尝试解析
            response_value = parse_read_discrete_input_response(response_bytes)
        
        if response_value is None:
            # 此条件现在主要覆盖解析失败的情况，因为无响应已提前返回False
            if response_bytes_for_debug: # 如果有收到数据但解析失败
                 print(f"警告: check_gripper_status 解析爪子状态响应失败 (原始数据: {binascii.hexlify(response_bytes_for_debug).decode()})")
            # 如果 response_bytes 为空，则上面的 "未收到串口响应" 已打印
            return False 
            
        # 根据X1的定义，0表示闭合（有物体），1表示打开（无物体）
        has_object = not bool(response_value & 0x01) 
        return has_object
            
    except ConnectionError as ce: # 捕获上面抛出的 ConnectionError
        print(f"[错误] check_gripper_status: {ce}")
        raise # 或者 return False，取决于上层如何处理
    except TimeoutError as te: # 如果选择在读取超时时抛出 TimeoutError
        print(f"[错误] check_gripper_status: {te}")
        return False # 或根据业务逻辑处理
    except Exception as e:
        print(f"[错误] check_gripper_status: 异常 {type(e).__name__} - {e}")
        print(traceback.format_exc())
        return False

def check_gripper_status_network(main_ser, main_serial_lock, main_config=None):
    """
    网络版本的爪子状态检查，使用传入的串口、锁和可选的配置对象。
    主要供 GameServiceServer 调用。

    Args:
        main_ser: 主程序管理的全局串口对象。
        main_serial_lock: 主程序管理的全局串口锁。
        main_config: (可选) 主程序加载的配置对象。如果为None, check_gripper_status会自行加载。
    
    Returns:
        bool: True表示闭合(检测到物体)，False表示打开(未检测到物体)
    """
    # 调用核心的爪子状态检查函数，传递必要的参数
    return check_gripper_status(ser=main_ser, config=main_config, serial_lock_obj=main_serial_lock)

if __name__ == "__main__":
    read_discrete_input_main()
