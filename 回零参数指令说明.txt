通过控制字（0x004E）的 Bit0-Bit6 进行控制，控制字每个位对应功能如下表所示
Bit4 回零控制位 0：无效； 1：有效；（不需要清零，重新置 1 即可）
以下数据为表格：
参数号  地址（十进制） 名称 设定范围 数据类型 属性
PA_040 64 回零方式    17、18、24、29、35 UNSIGNED 16 RW
PA_041 65 回零速度    0～3000 UNSIGNED16 RW
PA_042 66 回零爬行速度   0～3000 UNSIGNED16 RW
PA_043 67 回零加减速时间    0～2000 INTEGER16 RW
PA_044 68 原点偏移值H   -2147483648~2147483647 INTEGER32 RW
PA_045 69 原点偏移值L   -2147483648~2147483647  INTEGER32 RW
PA_04 4  运行状态   UNSIGNED16 RO
PA_08 8  当前位置（脉冲）H  INTEGER32 RO
PA_09 9  当前位置（脉冲）L  INTEGER32 RO
PA_0A 10  当前速度（r/min） INTEGER16 RO

其中PA_004只读的运行状态，各个bit意义如下：
Bit0 到位
Bit1 回零完成
Bit2 电机运行
Bit3 故障
Bit4 电机使能
Bit5 正软限位
Bit6 负软限位

1、负限位模式（PA_040=17）：启动回零后，电机以回零速度（PA_041）往负方向运行，当检测到
负限位开关后减速停止，然后以回零速度（PA_041）往正方向运行一段距离并减速停止，然后再以
回零爬行速度（PA_042）往负方向运行，当感应到负限位开关时，电机停止，回零动作完成。
2、正限位模式（PA_040=18）：启动回零后，电机以回零速度（PA_041）往正方向运行，当检测到
正限位开关后减速停止，然后以回零速度（PA_041）往负方向运行一段距离并减速停止，然后再以
回零爬行速度（PA_042）往正方向运行，当感应到正限位开关时，电机停止，回零动作完成。
3、正向原点模式（PA_040=24）：启动回零后，电机以回零速度（PA_041）往正方向运行，当检
测到原点开关后减速停止，然后以回零速度（PA_041）往负方向运行一段距离并减速停止，然后
再以回零爬行速度（PA_042）往正方向运行，当感应到原点开关时，电机停止，回零动作完成。
4、反向原点模式（PA_040=29）：启动回零后，电机以回零速度（PA_041）往负方向运行，当检测
到离开原点开关时减速停止，然后以回零爬行速度（PA_042）往正方向运行，当感应到原点开关时，
电机停止，回零动作完成。
5、设置当前位置为原点（PA_040=35）：启动回零后，直接将当前位置清零，然后输出回零完成
信号。

设置相关的回零参数：回零方式（PA_040）、回零速度（PA_041）、回零爬行速度（PA_042）、回
零加减速时间（PA_043）、回零偏移值（PA_044、PA_045），设置好后，用控制字（PA_04E）的 Bit4
来触发（上升沿）回零启动，当回零动作执行完成后，输出回零完成信号。

首先，硬件接线已经确定，参数PA_013（十进制地址19）设置值是3，即已经把DI2配置为负限位开关。

以 MODBUS 串口调试工具为例：
正向回原点模式
1、DI0 功能设置为原点开关（PA-011=1）
发送报文：01 06 00 11 00 01 18 0F
返回报文：01 06 00 11 00 01 18 0F
2、回零方式设置为正向原点模式 （PA-040=24）
发送报文：01 06 00 40 00 18 88 14
返回报文：01 06 00 40 00 18 88 14
3、运行控制字触发回零控制（PA-04E 的 bit5 置 1）
发送报文：01 06 00 4E 00 10 E8 11
返回报文：01 06 00 4E 00 10 E8 11
4、运行控制字触发停止（PA-04E 的 bit6 置 1）
发送报文：01 06 00 4E 00 20 E8 05
返回报文：01 06 00 4E 00 20 E8 05

负向回原点模式
1、DI0 功能设置为原点开关（PA-011=1）
发送报文：01 06 00 11 00 01 18 0F
返回报文：01 06 00 11 00 01 18 0F
2、回零方式设置为负向原点模式 （PA-040=29）
发送报文：01 06 00 40 00 1D 48 17
返回报文：01 06 00 40 00 1D 48 17
3、运行控制字触发回零控制（PA-04E 的 bit5 置 1）
发送报文：01 06 00 4E 00 10 E8 11
返回报文：01 06 00 4E 00 10 E8 11
4、运行控制字触发停止（PA-04E 的 bit6 置 1）
发送报文：01 06 00 4E 00 20 E8 05
返回报文：01 06 00 4E 00 20 E8 05
