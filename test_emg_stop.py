import time
from Move_Utils import load_motor_config, serial_lock, estimate_motion_time
from Move_commands import move_motor_with_speed, send_modbus_command, read_motor_status, read_motor_position

import datetime

def get_timestamp():
    """获取当前时间戳字符串，格式为HH:MM:SS.mmm"""
    now = datetime.datetime.now()
    return now.strftime("%H:%M:%S.") + f"{now.microsecond//1000:03d}"

def select_axis(config):
    axis_map = {
        "1": "motor_x",
        "2": "motor_y",
        "3": "motor_z",
        "4": "motor_cam"
    }
    axis_names = {
        "1": "X轴",
        "2": "Y轴",
        "3": "Z轴",
        "4": "Cam轴"
    }
    while True:
        print("请选择测试轴：")
        for k, v in axis_names.items():
            print(f"{k}: {v}")
        axis_choice = input("输入编号（1-4）：").strip()
        if axis_choice in axis_map:
            return axis_map[axis_choice], axis_names[axis_choice]
        print("输入无效，请重新选择。")

def input_distance(axis_key, axis_name, config):
    axis_cfg = config.get(axis_key, {})
    max_range = abs(axis_cfg.get("max_distance", 0))  # 修正这里
    while True:
        try:
            val = float(input(f"请输入{axis_name}移动距离（mm，正负均可，绝对值不超过{max_range}）：").strip())
            if max_range > 0 and abs(val) > max_range:
                print(f"超出最大行程（±{max_range}mm），请重新输入。")
                continue
            if abs(val) < 0.01:
                print("距离过小，请重新输入。")
                continue
            return val
        except Exception:
            print("输入无效，请重新输入数字。")

def select_stop_mode():
    while True:
        print("请选择停止模式：")
        print("1: 正常停止（Bit5=1，减速停车）")
        print("2: 急停（Bit6=1，立即停止）")
        mode = input("输入编号（1-2）：").strip()
        if mode in ("1", "2"):
            return mode
        print("输入无效，请重新选择。")

def get_slave_addr(config, axis_key):
    return config[axis_key]['slave_addr']

def print_motor_status(status):
    ts = get_timestamp()
    if status is None:
        print(f"[{ts}] 状态读取失败")
        return
    bit0_in_position = bool(status & 0x0001)
    bit2_running     = bool(status & 0x0004)
    bit3_fault       = bool(status & 0x0008)
    bit4_enabled     = bool(status & 0x0010)
    bit5_pos_limit   = bool(status & 0x0020)
    print(f"[{ts}] 到位 Bit0={bit0_in_position}，运行 Bit2={bit2_running}，故障 Bit3={bit3_fault}，使能 Bit4={bit4_enabled}，正软限位 Bit5={bit5_pos_limit}")

def print_motor_status_and_pos(status, pos, initial_pos, pulse_per_rev, lead, target_distance_mm):
    ts = get_timestamp()
    if status is None:
        print(f"[{ts}] 状态读取失败")
        return
    bit0_in_position = bool(status & 0x0001)
    bit2_running     = bool(status & 0x0004)
    bit3_fault       = bool(status & 0x0008)
    bit4_enabled     = bool(status & 0x0010)
    bit5_pos_limit   = bool(status & 0x0020)
    move_mm = None
    percent = None
    if pos is not None and initial_pos is not None and pulse_per_rev and lead:
        delta_pulse = pos - initial_pos
        move_mm = delta_pulse / pulse_per_rev * lead
        if target_distance_mm:
            percent = move_mm / abs(target_distance_mm) * 100
    msg = f"[{ts}] 到位 Bit0={bit0_in_position}，运行 Bit2={bit2_running}，故障 Bit3={bit3_fault}，使能 Bit4={bit4_enabled}，正软限位 Bit5={bit5_pos_limit}"
    if move_mm is not None and percent is not None:
        msg += f" | 实际移动 {move_mm:.3f} mm ({percent:.1f}%)"
    print(msg)

def main():
    print("=== 电机停止/急停功能测试 ===")
    config = load_motor_config()
    if not config:
        print("配置文件加载失败！")
        return

    axis_key, axis_name = select_axis(config)
    distance = input_distance(axis_key, axis_name, config)
    stop_mode = select_stop_mode()
    slave_addr = get_slave_addr(config, axis_key)

    # 打开串口
    from Move_Utils import open_serial, close_serial
    ser = open_serial(config['serial'])
    if not ser:
        print("串口打开失败！")
        return

    try:
        # 估算运动时间
        axis_cfg = config[axis_key]
        lead = axis_cfg['lead']
        speed = axis_cfg['speed']
        max_acc = axis_cfg.get('max_acceleration', 0.5)
        est_time = estimate_motion_time(distance, lead, speed, max_acc)
        print(f"估算总运动时间：{est_time:.2f} 秒，将在 {est_time*0.4:.2f} 秒时下发{'急停' if stop_mode=='2' else '正常停止'}命令。")

        # 运动前读取初始脉冲（提前到这里！）
        pulse_per_rev = axis_cfg.get('pulse_per_rev')
        lead = axis_cfg.get('lead')
        if not pulse_per_rev or not lead:
            print("配置缺少脉冲/导程参数，无法监控实际移动距离。")
            close_serial(ser)
            return
        initial_pos = read_motor_position(ser, slave_addr, serial_lock)
        if initial_pos is None:
            print("初始位置读取失败，无法监控实际移动距离。")
            close_serial(ser)
            return
        print(f"启动前脉冲数：{initial_pos}")

        monitor_status_delay = config.get('global_settings', {}).get('Monitor_status_delay', 0.1)

        # 启动运动
        print(f"开始运动：{axis_name}，距离 {distance} mm ...")
        move_motor_with_speed(ser, distance, config, axis_key, speed, serial_lock)

        # 等待40%时间
        time_to_wait = est_time * 0.4
        if time_to_wait > 0:
            time.sleep(time_to_wait)

        # 下发停止/急停命令
        if stop_mode == "1":
            print("下发正常停止命令（Bit5=1）...")
            data_stop = bytes([0x00, 0x4E, 0x00, 0x20])
            send_modbus_command(ser, slave_addr, 0x06, data_stop)
        else:
            print("下发急停命令（Bit6=1）...")
            data_emg_stop = bytes([0x00, 0x4E, 0x00, 0x40])
            send_modbus_command(ser, slave_addr, 0x06, data_emg_stop)

        print("命令已下发，持续监控电机状态（每0.2秒刷新一次，直到电机停止，且延迟采样）...")

        # 监控循环
        final_pos = None
        while True:
            status = read_motor_status(ser, slave_addr, serial_lock)
            pos = read_motor_position(ser, slave_addr, serial_lock)
            print_motor_status_and_pos(status, pos, initial_pos, pulse_per_rev, lead, distance)
            if status is None:
                print(f"[{get_timestamp()}] 状态读取失败，退出监控。")
                break
            bit2_running = bool(status & 0x0004)
            bit3_fault = bool(status & 0x0008)
            if not bit2_running:
                print(f"[{get_timestamp()}] 检测到电机已停止，进入延迟采样阶段...")
                # 延迟采样
                time.sleep(monitor_status_delay)
                status = read_motor_status(ser, slave_addr, serial_lock)
                pos = read_motor_position(ser, slave_addr, serial_lock)
                print_motor_status_and_pos(status, pos, initial_pos, pulse_per_rev, lead, distance)
                final_pos = pos
                print(f"结束时脉冲数：{final_pos}")
                if final_pos is not None and initial_pos is not None:
                    delta_pulse = final_pos - initial_pos
                    move_mm = delta_pulse / pulse_per_rev * lead
                    print(f"脉冲差值：{delta_pulse}，换算移动：{move_mm:.3f} mm")
                print(f"[{get_timestamp()}] 延迟采样完成，监控结束。")
                break
            if bit3_fault:
                print(f"[{get_timestamp()}] 检测到故障，退出监控。")
                break
            time.sleep(0.2)

        print("测试结束，串口即将关闭。")
    finally:
        close_serial(ser)
        print("串口已关闭。")

if __name__ == "__main__":
    main()