Uint16 COMM_CrcValueCalc(const Uint8 *data, Uint16 length)
{
    Uint16 crcValue = 0xffff; int16 i;
    while (length--)
    {
        crcValue ^= *data++; for (i = 0; i < 8; i++)
        {
            if (crcValue & 0x0001)
            {
                crcValue = (crcValue >> 1) ^ 0xA001;
            }
            else
            {
                crcValue = crcValue >> 1;
            }
        }
    }
    return (crcValue);
}