
[21:19:38.667] ===== 运动时间估算 =====
[21:19:38.667] Z轴移动距离: 0.0 mm
[21:19:38.667] 估算Z轴运动时间: 0.000 秒
[21:19:38.667] 所有轴中最长运动时间: 0.000 秒
[21:19:38.667] X、Y、Z轴同步移动时间: 0.000 秒
[21:19:38.667] =======================

[21:19:38.668] Controlling DO2 to: OFF
[21:19:38.689] No monitor threads to start for this command.
[21:19:38.716] No axes being monitored for completion in this command.
[21:19:38.716] All monitored axes for this command instance completed.
[21:19:38.716] run_motor_commands finished.
[21:19:38.716] 移动操作成功完成! 新电机位置: X=311.754, Y=389.305, Z=345.000, CAM=0.000
ReqID:unknown 物体编号:3 - 步骤 5: 等待抓取稳定时间 0.5秒
ReqID:unknown 物体编号:3 - 步骤 6: Z轴上升到临时高度 (50.0mm)

[21:19:39.217] ===== 准备移动 =====
[21:19:39.217] 请求目标位置: X=不变, Y=不变, Z=50.0, CAM=不变
[21:19:39.217] 调整后物理目标: X=381.7538693547249, Y=459.3050619959831
[21:19:39.217] 当前电机位置: X=311.754, Y=389.305, Z=345.000, CAM=0.000
[21:19:39.217] 使用的轴偏移值 (来自配置): X=70.000, Y=70.000
[21:19:39.217] 计算电机目标: X=311.754, Y=389.305, Z=50.000, CAM=0.000
[21:19:39.217] 计算移动距离: dX=+0.000, dY=+0.000, dZ=-295.000, dCAM=+0.000
[21:19:39.217] DO2状态: 2 (不变(2))
[21:19:39.217] =====================
[21:19:39.220] run_motor_commands invoked.

[21:19:39.220] ===== 全局速度设置 =====
[21:19:39.220] 最大速度限制: 600 RPM
[21:19:39.220] ========================

[21:19:39.220] ===== Z轴配置信息 =====
[21:19:39.220] Z轴导程: 21.0 mm/转
[21:19:39.220] Z轴设置速度: 400 RPM
[21:19:39.220] Z轴实际速度: 400 RPM
[21:19:39.220] Z轴最大加速度: 0.4 m/s²
[21:19:39.220] Z轴从站地址: 4
[21:19:39.220] ========================

--- 各轴运动时间估算 ---
全局最大速度限制: 600 RPM
motor_x: 不移动，时间为0
motor_y: 不移动，时间为0

计算motor_z轴，距离=-295.0mm:

--- 运动时间估算详情 ---
导程: 21.0 mm/转
目标速度: 400 RPM = 6.6667 转/秒 = 0.1400 m/s
加速度: 0.4 m/s²
加速时间: 0.3500秒
减速时间: 0.3500秒
加速位移: 0.0245 m
减速位移: 0.0245 m
总位移: 0.2950 m
情况: 达到最高速度，梯形速度曲线
加速时间: 0.3500秒
匀速距离: 0.2460m
匀速时间: 1.7571秒
减速时间: 0.3500秒
总估算时间: 2.4571秒 (加速+匀速+减速)
----------------------
motor_z估算时间: 2.457秒
motor_cam: 不移动，时间为0

--- 同步移动时间计算 ---
X轴时间: 0.000秒
Y轴时间: 0.000秒
Z轴时间: 2.457秒
同步移动时间: 2.457秒
Z轴: 应用同步速度补偿系数 1.70。速度从 400.0 RPM 调整为 680.0 RPM。
Z轴: 补偿后速度 680.0 RPM 超过有效最大限制 400.0 RPM，将使用限制值。
motor_z轴: 调整速度为 400.0 RPM 以达到同步时间 2.457秒

所有轴时间结果:
 - motor_x: 0.000秒
 - motor_y: 0.000秒
 - motor_z: 2.457秒
 - motor_cam: 0.000秒
Z轴时间: 2.457秒
最长运动时间: 2.457秒
同步移动时间: 2.457秒

DEBUG - 最终调整后的速度值:
motor_x: 150.000 RPM
motor_y: 180.000 RPM
motor_z: 400.000 RPM
motor_cam: 200.000 RPM
------------------------

[21:19:39.220] ===== 运动时间估算 =====
[21:19:39.220] Z轴移动距离: -295.0 mm
[21:19:39.220] 估算Z轴运动时间: 2.457 秒
[21:19:39.220] 所有轴中最长运动时间: 2.457 秒
[21:19:39.220] X、Y、Z轴同步移动时间: 2.457 秒
[21:19:39.220] =======================

[21:19:39.220] Pre-Z delay: 0.04s, then opening DO1 brake.
[21:19:39.289] Sending Z command: -295.000 mm, Speed: 400.0 RPM

--- 运动时间估算详情 ---
导程: 21.0 mm/转
目标速度: 400 RPM = 6.6667 转/秒 = 0.1400 m/s
加速度: 0.4 m/s²
加速时间: 0.3500秒
减速时间: 0.3500秒
加速位移: 0.0245 m
减速位移: 0.0245 m
总位移: 0.2950 m
情况: 达到最高速度，梯形速度曲线
加速时间: 0.3500秒
匀速距离: 0.2460m
匀速时间: 1.7571秒
减速时间: 0.3500秒
总估算时间: 2.4571秒 (加速+匀速+减速)
----------------------
[21:19:39.339] Starting 1 monitor thread(s) for this command...
[21:19:39.340] Z-Axis Monitor (Addr: 4): Started.
[21:19:39.340] All 1 monitor threads for this command have been started.
[21:19:39.340] Now waiting for 1 monitored axis/axes of this command to complete...
[21:19:39.340] Waiting for motor_z to complete...
[21:19:42.420] Z-axis (Addr: 4) status: 电机停止 (Status: 0x0002). Activating brake.
[21:19:42.441] Z-Axis Monitor (Addr: 4): Ended (normal or error).
[21:19:42.471] motor_z completed.
[21:19:42.472] All monitored axes for this command instance completed.
[21:19:42.473] Post-Z delay: 0.0s, then closing DO1 brake.
[21:19:42.473] Ensuring DO1 brake is closed.
[21:19:42.494] run_motor_commands finished.
[21:19:42.500] 移动操作成功完成! 新电机位置: X=311.754, Y=389.305, Z=50.000 (已更新), CAM=0.000
ReqID:unknown 物体编号:3 - 步骤 7: 检查爪子状态，判断是否抓取到物体
ReqID:unknown 物体编号:3 - 未检测到物体，可能抓取失败
ReqID:unknown 物体编号:3 - 步骤 8: 打开爪子(DO2=1) - 抓取失败路径

[21:19:42.558] ===== 准备移动 =====
[21:19:42.558] 请求目标位置: X=不变, Y=不变, Z=不变, CAM=不变
[21:19:42.558] 调整后物理目标: X=381.7538693547249, Y=459.3050619959831
[21:19:42.558] 当前电机位置: X=311.754, Y=389.305, Z=50.000, CAM=0.000
[21:19:42.558] 使用的轴偏移值 (来自配置): X=70.000, Y=70.000
[21:19:42.558] 计算电机目标: X=311.754, Y=389.305, Z=50.000, CAM=0.000
[21:19:42.558] 计算移动距离: dX=+0.000, dY=+0.000, dZ=+0.000, dCAM=+0.000
[21:19:42.558] DO2状态: 1 (释放(1))
[21:19:42.558] =====================
[21:19:42.559] run_motor_commands invoked.

[21:19:42.559] ===== 全局速度设置 =====
[21:19:42.559] 最大速度限制: 600 RPM
[21:19:42.559] ========================

[21:19:42.559] ===== Z轴配置信息 =====
[21:19:42.559] Z轴导程: 21.0 mm/转
[21:19:42.559] Z轴设置速度: 400 RPM
[21:19:42.559] Z轴实际速度: 400 RPM
[21:19:42.559] Z轴最大加速度: 0.4 m/s²
[21:19:42.559] Z轴从站地址: 4
[21:19:42.559] ========================

--- 各轴运动时间估算 ---
全局最大速度限制: 600 RPM
motor_x: 不移动，时间为0
motor_y: 不移动，时间为0
motor_z: 不移动，时间为0
motor_cam: 不移动，时间为0

--- 同步移动时间计算 ---
X轴时间: 0.000秒
Y轴时间: 0.000秒
Z轴时间: 0.000秒
同步移动时间: 0.000秒

所有轴时间结果:
 - motor_x: 0.000秒
 - motor_y: 0.000秒
 - motor_z: 0.000秒
 - motor_cam: 0.000秒
Z轴时间: 0.000秒
最长运动时间: 0.000秒
同步移动时间: 0.000秒

DEBUG - 最终调整后的速度值:
motor_x: 150.000 RPM
motor_y: 180.000 RPM
motor_z: 400.000 RPM
motor_cam: 200.000 RPM
------------------------

[21:19:42.564] ===== 运动时间估算 =====
[21:19:42.564] Z轴移动距离: 0.0 mm
[21:19:42.564] 估算Z轴运动时间: 0.000 秒
[21:19:42.564] 所有轴中最长运动时间: 0.000 秒
[21:19:42.564] X、Y、Z轴同步移动时间: 0.000 秒
[21:19:42.564] =======================

[21:19:42.564] Controlling DO2 to: ON
[21:19:42.587] No monitor threads to start for this command.
[21:19:42.587] No axes being monitored for completion in this command.
[21:19:42.587] All monitored axes for this command instance completed.
[21:19:42.587] run_motor_commands finished.
[21:19:42.587] 移动操作成功完成! 新电机位置: X=311.754, Y=389.305, Z=50.000, CAM=0.000
ReqID:unknown 物体编号:3 - 步骤 9: 移动到展示位置 (400.0, 342.0, 0.0) - 抓取失败路径

[21:19:42.587] ===== 准备移动 =====
[21:19:42.587] 请求目标位置: X=400.0, Y=342.0, Z=0.0, CAM=不变
[21:19:42.587] 调整后物理目标: X=400.0, Y=342.0
[21:19:42.587] 当前电机位置: X=311.754, Y=389.305, Z=50.000, CAM=0.000
[21:19:42.587] 使用的轴偏移值 (来自配置): X=70.000, Y=70.000
[21:19:42.587] 计算电机目标: X=330.000, Y=272.000, Z=0.000, CAM=0.000
[21:19:42.587] 计算移动距离: dX=+18.246, dY=-117.305, dZ=-50.000, dCAM=+0.000
[21:19:42.587] DO2状态: 2 (不变(2))
[21:19:42.587] =====================
[21:19:42.587] run_motor_commands invoked.

[21:19:42.587] ===== 全局速度设置 =====
[21:19:42.587] 最大速度限制: 600 RPM
[21:19:42.587] ========================

[21:19:42.587] ===== Z轴配置信息 =====
[21:19:42.587] Z轴导程: 21.0 mm/转
[21:19:42.587] Z轴设置速度: 400 RPM
[21:19:42.587] Z轴实际速度: 400 RPM
[21:19:42.587] Z轴最大加速度: 0.4 m/s²
[21:19:42.587] Z轴从站地址: 4
[21:19:42.587] ========================

--- 各轴运动时间估算 ---
全局最大速度限制: 600 RPM

计算motor_x轴，距离=18.246130645275116mm:

--- 运动时间估算详情 ---
导程: 105.0 mm/转
目标速度: 150 RPM = 2.5000 转/秒 = 0.2625 m/s
加速度: 0.6 m/s²
加速时间: 0.4375秒
减速时间: 0.4375秒
加速位移: 0.0574 m
减速位移: 0.0574 m
总位移: 0.0182 m
情况: 未达到最高速度，三角形速度曲线
峰值速度: 0.1480 m/s = 84.6 RPM
调整后加减速时间: 0.2466秒 x 2
估算时间: 0.4932秒
----------------------
motor_x估算时间: 0.493秒

计算motor_y轴，距离=-117.30506199598312mm:

--- 运动时间估算详情 ---
导程: 75.0 mm/转
目标速度: 180 RPM = 3.0000 转/秒 = 0.2250 m/s
加速度: 0.5 m/s²
加速时间: 0.4500秒
减速时间: 0.4500秒
加速位移: 0.0506 m
减速位移: 0.0506 m
总位移: 0.1173 m
情况: 达到最高速度，梯形速度曲线
加速时间: 0.4500秒
匀速距离: 0.0161m
匀速时间: 0.0714秒
减速时间: 0.4500秒
总估算时间: 0.9714秒 (加速+匀速+减速)
----------------------
motor_y估算时间: 0.971秒

计算motor_z轴，距离=-50.0mm:

--- 运动时间估算详情 ---
导程: 21.0 mm/转
目标速度: 400 RPM = 6.6667 转/秒 = 0.1400 m/s
加速度: 0.4 m/s²
加速时间: 0.3500秒
减速时间: 0.3500秒
加速位移: 0.0245 m
减速位移: 0.0245 m
总位移: 0.0500 m
情况: 达到最高速度，梯形速度曲线
加速时间: 0.3500秒
匀速距离: 0.0010m
匀速时间: 0.0071秒
减速时间: 0.3500秒
总估算时间: 0.7071秒 (加速+匀速+减速)
----------------------
motor_z估算时间: 0.707秒
motor_cam: 不移动，时间为0

--- 同步移动时间计算 ---
X轴时间: 0.493秒
Y轴时间: 0.971秒
Z轴时间: 0.707秒
同步移动时间: 0.971秒
motor_x轴: 调整速度为 11.1 RPM 以达到同步时间 0.971秒
motor_y轴: 计算同步速度 180.0 RPM 超过有效最大限制 180.0 RPM，将使用限制值。
motor_y轴: 调整速度为 180.0 RPM 以达到同步时间 0.971秒
Z轴: 应用同步速度补偿系数 1.70。速度从 174.5 RPM 调整为 296.6 RPM。
motor_z轴: 调整速度为 296.6 RPM 以达到同步时间 0.971秒

所有轴时间结果:
 - motor_x: 0.493秒
 - motor_y: 0.971秒
 - motor_z: 0.707秒
 - motor_cam: 0.000秒
Z轴时间: 0.707秒
最长运动时间: 0.971秒
同步移动时间: 0.971秒

DEBUG - 最终调整后的速度值:
motor_x: 11.104 RPM
motor_y: 180.000 RPM
motor_z: 296.649 RPM
motor_cam: 200.000 RPM
------------------------

[21:19:42.597] ===== 运动时间估算 =====
[21:19:42.597] Z轴移动距离: -50.0 mm
[21:19:42.597] 估算Z轴运动时间: 0.707 秒
[21:19:42.597] 所有轴中最长运动时间: 0.971 秒
[21:19:42.597] X、Y、Z轴同步移动时间: 0.971 秒
[21:19:42.597] =======================

[21:19:42.598] Pre-Z delay: 0.04s, then opening DO1 brake.
[21:19:42.669] Sending X command: 18.246 mm, Speed: 11.1 RPM

--- 运动时间估算详情 ---
导程: 105.0 mm/转
目标速度: 11 RPM = 0.1833 转/秒 = 0.0192 m/s
加速度: 0.6 m/s²
加速时间: 0.0321秒
减速时间: 0.0321秒
加速位移: 0.0003 m
减速位移: 0.0003 m
总位移: 0.0182 m
情况: 达到最高速度，梯形速度曲线
加速时间: 0.0321秒
匀速距离: 0.0176m
匀速时间: 0.9158秒
减速时间: 0.0321秒
总估算时间: 0.9799秒 (加速+匀速+减速)
----------------------
[21:19:42.719] Sending Y command: -117.305 mm, Speed: 180.0 RPM

--- 运动时间估算详情 ---
导程: 75.0 mm/转
目标速度: 180 RPM = 3.0000 转/秒 = 0.2250 m/s
加速度: 0.5 m/s²
加速时间: 0.4500秒
减速时间: 0.4500秒
加速位移: 0.0506 m
减速位移: 0.0506 m
总位移: 0.1173 m
情况: 达到最高速度，梯形速度曲线
加速时间: 0.4500秒
匀速距离: 0.0161m
匀速时间: 0.0714秒
减速时间: 0.4500秒
总估算时间: 0.9714秒 (加速+匀速+减速)
----------------------
[21:19:42.774] Sending Z command: -50.000 mm, Speed: 296.6 RPM

--- 运动时间估算详情 ---
导程: 21.0 mm/转
目标速度: 297 RPM = 4.9500 转/秒 = 0.1040 m/s
加速度: 0.4 m/s²
加速时间: 0.2599秒
减速时间: 0.2599秒
加速位移: 0.0135 m
减速位移: 0.0135 m
总位移: 0.0500 m
情况: 达到最高速度，梯形速度曲线
加速时间: 0.2599秒
匀速距离: 0.0230m
匀速时间: 0.2211秒
减速时间: 0.2599秒
总估算时间: 0.7409秒 (加速+匀速+减速)
----------------------
[21:19:42.825] Starting 3 monitor thread(s) for this command...
[21:19:42.825] motor_x (Addr: 3): Monitoring started.
[21:19:42.826] motor_y (Addr: 2): Monitoring started.
[21:19:42.826] Z-Axis Monitor (Addr: 4): Started.
[21:19:42.827] All 3 monitor threads for this command have been started.
[21:19:42.827] Now waiting for 3 monitored axis/axes of this command to complete...
[21:19:42.827] Waiting for motor_x to complete...
[21:19:42.877] motor_x: Initial status is RUNNING.
[21:19:42.927] motor_y: Initial status is RUNNING.
[21:19:43.742] Z-axis (Addr: 4) status: 电机停止 (Status: 0x0002). Activating brake.
[21:19:43.814] Z-Axis Monitor (Addr: 4): Ended (normal or error).
[21:19:43.937] motor_x completed.
[21:19:43.937] Waiting for motor_y to complete...
[21:19:43.937] motor_y completed.
[21:19:43.937] Waiting for motor_z to complete...
[21:19:43.937] motor_z completed.
[21:19:43.937] All monitored axes for this command instance completed.
[21:19:43.937] Post-Z delay: 0.0s, then closing DO1 brake.
[21:19:43.937] Ensuring DO1 brake is closed.
[21:19:43.960] run_motor_commands finished.
[21:19:43.961] 移动操作成功完成! 新电机位置: X=330.000 (已更新), Y=272.000 (已更新), Z=0.000 (已更新), CAM=0.000
ReqID:unknown 物体编号:3 - 步骤 10: 展示位置停留 1.0秒 - 抓取失败路径
ReqID:unknown 物体编号:3 - 步骤 11: 回到初始位置 - 抓取失败路径

[21:19:44.962] ===== 准备移动 =====
[21:19:44.962] 请求目标位置: X=70.0, Y=70.0, Z=0.0, CAM=不变
[21:19:44.962] 调整后物理目标: X=70.0, Y=70.0
[21:19:44.962] 当前电机位置: X=330.000, Y=272.000, Z=0.000, CAM=0.000
[21:19:44.962] 使用的轴偏移值 (来自配置): X=70.000, Y=70.000
[21:19:44.962] 计算电机目标: X=0.000, Y=0.000, Z=0.000, CAM=0.000
[21:19:44.962] 计算移动距离: dX=-330.000, dY=-272.000, dZ=+0.000, dCAM=+0.000
[21:19:44.962] DO2状态: 2 (不变(2))
[21:19:44.962] =====================
[21:19:44.962] run_motor_commands invoked.

[21:19:44.962] ===== 全局速度设置 =====
[21:19:44.962] 最大速度限制: 600 RPM
[21:19:44.962] ========================

[21:19:44.962] ===== Z轴配置信息 =====
[21:19:44.962] Z轴导程: 21.0 mm/转
[21:19:44.962] Z轴设置速度: 400 RPM
[21:19:44.962] Z轴实际速度: 400 RPM
[21:19:44.962] Z轴最大加速度: 0.4 m/s²
[21:19:44.962] Z轴从站地址: 4
[21:19:44.962] ========================

--- 各轴运动时间估算 ---
全局最大速度限制: 600 RPM

计算motor_x轴，距离=-330.0mm:

--- 运动时间估算详情 ---
导程: 105.0 mm/转
目标速度: 150 RPM = 2.5000 转/秒 = 0.2625 m/s
加速度: 0.6 m/s²
加速时间: 0.4375秒
减速时间: 0.4375秒
加速位移: 0.0574 m
减速位移: 0.0574 m
总位移: 0.3300 m
情况: 达到最高速度，梯形速度曲线
加速时间: 0.4375秒
匀速距离: 0.2152m
匀速时间: 0.8196秒
减速时间: 0.4375秒
总估算时间: 1.6946秒 (加速+匀速+减速)
----------------------
motor_x估算时间: 1.695秒

计算motor_y轴，距离=-272.0mm:

--- 运动时间估算详情 ---
导程: 75.0 mm/转
目标速度: 180 RPM = 3.0000 转/秒 = 0.2250 m/s
加速度: 0.5 m/s²
加速时间: 0.4500秒
减速时间: 0.4500秒
加速位移: 0.0506 m
减速位移: 0.0506 m
总位移: 0.2720 m
情况: 达到最高速度，梯形速度曲线
加速时间: 0.4500秒
匀速距离: 0.1708m
匀速时间: 0.7589秒
减速时间: 0.4500秒
总估算时间: 1.6589秒 (加速+匀速+减速)
----------------------
motor_y估算时间: 1.659秒
motor_z: 不移动，时间为0
motor_cam: 不移动，时间为0

--- 同步移动时间计算 ---
X轴时间: 1.695秒
Y轴时间: 1.659秒
Z轴时间: 0.000秒
同步移动时间: 1.695秒
motor_x轴: 调整速度为 150.0 RPM 以达到同步时间 1.695秒
motor_y轴: 调整速度为 172.1 RPM 以达到同步时间 1.695秒

所有轴时间结果:
 - motor_x: 1.695秒
 - motor_y: 1.659秒
 - motor_z: 0.000秒
 - motor_cam: 0.000秒
Z轴时间: 0.000秒
最长运动时间: 1.695秒
同步移动时间: 1.695秒

DEBUG - 最终调整后的速度值:
motor_x: 150.000 RPM
motor_y: 172.098 RPM
motor_z: 400.000 RPM
motor_cam: 200.000 RPM
------------------------

[21:19:44.981] ===== 运动时间估算 =====
[21:19:44.981] Z轴移动距离: 0.0 mm
[21:19:44.981] 估算Z轴运动时间: 0.000 秒
[21:19:44.981] 所有轴中最长运动时间: 1.695 秒
[21:19:44.981] X、Y、Z轴同步移动时间: 1.695 秒
[21:19:44.981] =======================

[21:19:44.982] Sending X command: -330.000 mm, Speed: 150.0 RPM

--- 运动时间估算详情 ---
导程: 105.0 mm/转
目标速度: 150 RPM = 2.5000 转/秒 = 0.2625 m/s
加速度: 0.6 m/s²
加速时间: 0.4375秒
减速时间: 0.4375秒
加速位移: 0.0574 m
减速位移: 0.0574 m
总位移: 0.3300 m
情况: 达到最高速度，梯形速度曲线
加速时间: 0.4375秒
匀速距离: 0.2152m
匀速时间: 0.8196秒
减速时间: 0.4375秒
总估算时间: 1.6946秒 (加速+匀速+减速)
----------------------
[21:19:45.030] Sending Y command: -272.000 mm, Speed: 172.1 RPM

--- 运动时间估算详情 ---
导程: 75.0 mm/转
目标速度: 172 RPM = 2.8667 转/秒 = 0.2150 m/s
加速度: 0.5 m/s²
加速时间: 0.4300秒
减速时间: 0.4300秒
加速位移: 0.0462 m
减速位移: 0.0462 m
总位移: 0.2720 m
情况: 达到最高速度，梯形速度曲线
加速时间: 0.4300秒
匀速距离: 0.1796m
匀速时间: 0.8351秒
减速时间: 0.4300秒
总估算时间: 1.6951秒 (加速+匀速+减速)
----------------------
[21:19:45.088] Starting 2 monitor thread(s) for this command...
[21:19:45.088] motor_x (Addr: 3): Monitoring started.
[21:19:45.089] motor_y (Addr: 2): Monitoring started.
[21:19:45.089] All 2 monitor threads for this command have been started.
[21:19:45.089] Now waiting for 2 monitored axis/axes of this command to complete...
[21:19:45.089] Waiting for motor_x to complete...
[21:19:45.140] motor_x: Initial status is RUNNING.
[21:19:45.192] motor_y: Initial status is RUNNING.
[21:19:46.857] motor_x completed.
[21:19:46.857] Waiting for motor_y to complete...
[21:19:46.914] motor_y completed.
[21:19:46.914] All monitored axes for this command instance completed.
[21:19:46.924] run_motor_commands finished.
[21:19:46.924] 移动操作成功完成! 新电机位置: X=0.000 (已更新), Y=0.000 (已更新), Z=0.000 (已更新), CAM=0.000
ReqID:unknown 物体编号:3 - 未抓取到物体，本轮操作结束
[21:19:46.925] ReqID:unknown 物体编号:3 - 抓取周期函数 move_to_object 执行完毕，将触发配置重载。

[21:19:46.925] ===== 游戏周期结束，检查配置更新 =====
[21:19:46.926] 配置文件 Config_Move.yaml 修改时间变化: 1750425336.803409 -> 1750425560.8312488
[21:19:46.926] 配置文件已修改，开始执行动态配置更新...
[21:19:46.926] 正在从 Config_Move.yaml 重新加载动态配置...
  - 已更新部分: global_settings
  - 已更新部分: fish_tank
  - 已更新部分: display_pos
  - 已更新部分: catch_settings
  - 已更新部分: temp_height
  - 已更新部分: camera_showcase_settings
  - 已更新部分: release_settings
  - 已更新部分: homing
  - 正在更新 motor_x 的参数:
  - 正在更新 motor_y 的参数:
  - 正在更新 motor_z 的参数:
  - 正在更新 motor_cam 的参数:
[21:19:46.984] 动态配置更新完成。
[21:19:46.985] 已更新配置文件 Config_Move.yaml 的修改时间缓存: 1750425560.8312488
[21:19:46.985] 配置已更新，刷新相关的全局变量...
[21:19:46.985] 轴偏移已更新: X=70.0, Y=70.0
[21:19:46.985] 配置更新完成
[21:19:46.985] ===== 配置检查流程结束 =====


当前检测到 3 个物体:
--------------------------------------------------------------------------------------
编号  |  类别ID      |  物理坐标(mm)   | 归一化比例 | 置信度 | 估算时间(秒)
--------------------------------------------------------------------------------------
2     | 1  (小龙虾   ) | (397.0, 470.2)  | (0.580, 0.686)     | 0.91   | N/A
3     | 2  (贝壳    ) | (532.8, 31.8)   | (0.778, 0.046)     | 0.91   | N/A
1     | 0  (螃蟹    ) | (171.6, 221.9)  | (0.251, 0.324)     | 0.84   | N/A
--------------------------------------------------------------------------------------
输入物体编号移动到该位置，输入'r'刷新物体列表，输入'q'退出

请输入物体编号或命令(r=刷新, q=退出): r
刷新物体列表...

当前检测到 3 个物体:
--------------------------------------------------------------------------------------
编号  |  类别ID      |  物理坐标(mm)   | 归一化比例 | 置信度 | 估算时间(秒)
--------------------------------------------------------------------------------------
1     | 0  (螃蟹    ) | (172.1, 217.6)  | (0.251, 0.318)     | 0.94   | N/A
2     | 2  (贝壳    ) | (435.1, 171.2)  | (0.635, 0.250)     | 0.91   | N/A
3     | 1  (小龙虾   ) | (396.8, 470.6)  | (0.579, 0.687)     | 0.91   | N/A
--------------------------------------------------------------------------------------
输入物体编号移动到该位置，输入'r'刷新物体列表，输入'q'退出

请输入物体编号或命令(r=刷新, q=退出): 1

ReqID:unknown 物体编号:1 - 准备移动到物体 #1 (螃蟹)
ReqID:unknown 物体编号:1 - 物理坐标: (172.1, 217.6) mm

ReqID:unknown 物体编号:1 - ===== 开始抓取流程 =====
ReqID:unknown 物体编号:1 - 步骤 1: 打开爪子(DO2=1)

[21:20:06.023] ===== 准备移动 =====
[21:20:06.023] 请求目标位置: X=不变, Y=不变, Z=不变, CAM=不变
[21:20:06.023] 调整后物理目标: X=70.0, Y=70.0
[21:20:06.023] 当前电机位置: X=0.000, Y=0.000, Z=0.000, CAM=0.000
[21:20:06.023] 使用的轴偏移值 (来自配置): X=70.000, Y=70.000
[21:20:06.023] 计算电机目标: X=0.000, Y=0.000, Z=0.000, CAM=0.000
[21:20:06.023] 计算移动距离: dX=+0.000, dY=+0.000, dZ=+0.000, dCAM=+0.000
[21:20:06.023] DO2状态: 1 (释放(1))
[21:20:06.023] =====================
[21:20:06.023] run_motor_commands invoked.

[21:20:06.023] ===== 全局速度设置 =====
[21:20:06.023] 最大速度限制: 600 RPM
[21:20:06.023] ========================

[21:20:06.023] ===== Z轴配置信息 =====
[21:20:06.023] Z轴导程: 21.0 mm/转
[21:20:06.023] Z轴设置速度: 400 RPM
[21:20:06.023] Z轴实际速度: 400 RPM
[21:20:06.023] Z轴最大加速度: 0.4 m/s²
[21:20:06.023] Z轴从站地址: 4
[21:20:06.023] ========================

--- 各轴运动时间估算 ---
全局最大速度限制: 600 RPM
motor_x: 不移动，时间为0
motor_y: 不移动，时间为0
motor_z: 不移动，时间为0
motor_cam: 不移动，时间为0

--- 同步移动时间计算 ---
X轴时间: 0.000秒
Y轴时间: 0.000秒
Z轴时间: 0.000秒
同步移动时间: 0.000秒

所有轴时间结果:
 - motor_x: 0.000秒
 - motor_y: 0.000秒
 - motor_z: 0.000秒
 - motor_cam: 0.000秒
Z轴时间: 0.000秒
最长运动时间: 0.000秒
同步移动时间: 0.000秒

DEBUG - 最终调整后的速度值:
motor_x: 150.000 RPM
motor_y: 180.000 RPM
motor_z: 400.000 RPM
motor_cam: 200.000 RPM
------------------------

[21:20:06.025] ===== 运动时间估算 =====
[21:20:06.025] Z轴移动距离: 0.0 mm
[21:20:06.025] 估算Z轴运动时间: 0.000 秒
[21:20:06.025] 所有轴中最长运动时间: 0.000 秒
[21:20:06.025] X、Y、Z轴同步移动时间: 0.000 秒
[21:20:06.025] =======================

[21:20:06.025] Controlling DO2 to: ON
[21:20:06.046] No monitor threads to start for this command.
[21:20:06.046] No axes being monitored for completion in this command.
[21:20:06.046] All monitored axes for this command instance completed.
[21:20:06.046] run_motor_commands finished.
[21:20:06.046] 移动操作成功完成! 新电机位置: X=0.000, Y=0.000, Z=0.000, CAM=0.000
ReqID:unknown 物体编号:1 - 步骤 2: 移动X、Y轴到目标位置 (172.1, 217.6)

[21:20:06.247] ===== 准备移动 =====
[21:20:06.247] 请求目标位置: X=172.13764682412148, Y=217.55545690655708, Z=不变, CAM=不变
[21:20:06.247] 调整后物理目标: X=172.13764682412148, Y=217.55545690655708
[21:20:06.247] 当前电机位置: X=0.000, Y=0.000, Z=0.000, CAM=0.000
[21:20:06.247] 使用的轴偏移值 (来自配置): X=70.000, Y=70.000
[21:20:06.247] 计算电机目标: X=102.138, Y=147.555, Z=0.000, CAM=0.000
[21:20:06.247] 计算移动距离: dX=+102.138, dY=+147.555, dZ=+0.000, dCAM=+0.000
[21:20:06.247] DO2状态: 2 (不变(2))
[21:20:06.247] =====================
[21:20:06.247] run_motor_commands invoked.

[21:20:06.247] ===== 全局速度设置 =====
[21:20:06.247] 最大速度限制: 600 RPM
[21:20:06.247] ========================

[21:20:06.247] ===== Z轴配置信息 =====
[21:20:06.247] Z轴导程: 21.0 mm/转
[21:20:06.247] Z轴设置速度: 400 RPM
[21:20:06.247] Z轴实际速度: 400 RPM
[21:20:06.247] Z轴最大加速度: 0.4 m/s²
[21:20:06.247] Z轴从站地址: 4
[21:20:06.247] ========================

--- 各轴运动时间估算 ---
全局最大速度限制: 600 RPM

计算motor_x轴，距离=102.13764682412148mm:

--- 运动时间估算详情 ---
导程: 105.0 mm/转
目标速度: 150 RPM = 2.5000 转/秒 = 0.2625 m/s
加速度: 0.6 m/s²
加速时间: 0.4375秒
减速时间: 0.4375秒
加速位移: 0.0574 m
减速位移: 0.0574 m
总位移: 0.1021 m
情况: 未达到最高速度，三角形速度曲线
峰值速度: 0.3501 m/s = 200.1 RPM
调整后加减速时间: 0.5835秒 x 2
估算时间: 1.1670秒
----------------------
motor_x估算时间: 1.167秒

计算motor_y轴，距离=147.55545690655708mm:

--- 运动时间估算详情 ---
导程: 75.0 mm/转
目标速度: 180 RPM = 3.0000 转/秒 = 0.2250 m/s
加速度: 0.5 m/s²
加速时间: 0.4500秒
减速时间: 0.4500秒
加速位移: 0.0506 m
减速位移: 0.0506 m
总位移: 0.1476 m
情况: 达到最高速度，梯形速度曲线
加速时间: 0.4500秒
匀速距离: 0.0463m
匀速时间: 0.2058秒
减速时间: 0.4500秒
总估算时间: 1.1058秒 (加速+匀速+减速)
----------------------
motor_y估算时间: 1.106秒
motor_z: 不移动，时间为0
motor_cam: 不移动，时间为0

--- 同步移动时间计算 ---
X轴时间: 1.167秒
Y轴时间: 1.106秒
Z轴时间: 0.000秒
同步移动时间: 1.167秒
motor_x轴: 调整速度为 58.6 RPM 以达到同步时间 1.167秒
motor_y轴: 调整速度为 148.2 RPM 以达到同步时间 1.167秒

所有轴时间结果:
 - motor_x: 1.167秒
 - motor_y: 1.106秒
 - motor_z: 0.000秒
 - motor_cam: 0.000秒
Z轴时间: 0.000秒
最长运动时间: 1.167秒
同步移动时间: 1.167秒

DEBUG - 最终调整后的速度值:
motor_x: 58.594 RPM
motor_y: 148.215 RPM
motor_z: 400.000 RPM
motor_cam: 200.000 RPM
------------------------

[21:20:06.247] ===== 运动时间估算 =====
[21:20:06.247] Z轴移动距离: 0.0 mm
[21:20:06.247] 估算Z轴运动时间: 0.000 秒
[21:20:06.247] 所有轴中最长运动时间: 1.167 秒
[21:20:06.247] X、Y、Z轴同步移动时间: 1.167 秒
[21:20:06.247] =======================

[21:20:06.247] Sending X command: 102.138 mm, Speed: 58.6 RPM

--- 运动时间估算详情 ---
导程: 105.0 mm/转
目标速度: 59 RPM = 0.9833 转/秒 = 0.1032 m/s
加速度: 0.6 m/s²
加速时间: 0.1721秒
减速时间: 0.1721秒
加速位移: 0.0089 m
减速位移: 0.0089 m
总位移: 0.1021 m
情况: 达到最高速度，梯形速度曲线
加速时间: 0.1721秒
匀速距离: 0.0844m
匀速时间: 0.8171秒
减速时间: 0.1721秒
总估算时间: 1.1613秒 (加速+匀速+减速)
----------------------
[21:20:06.303] Sending Y command: 147.555 mm, Speed: 148.2 RPM

--- 运动时间估算详情 ---
导程: 75.0 mm/转
目标速度: 148 RPM = 2.4667 转/秒 = 0.1850 m/s
加速度: 0.5 m/s²
加速时间: 0.3700秒
减速时间: 0.3700秒
加速位移: 0.0342 m
减速位移: 0.0342 m
总位移: 0.1476 m
情况: 达到最高速度，梯形速度曲线
加速时间: 0.3700秒
匀速距离: 0.0791m
匀速时间: 0.4276秒
减速时间: 0.3700秒
总估算时间: 1.1676秒 (加速+匀速+减速)
----------------------
[21:20:06.361] Starting 2 monitor thread(s) for this command...
[21:20:06.361] motor_x (Addr: 3): Monitoring started.
[21:20:06.361] motor_y (Addr: 2): Monitoring started.
[21:20:06.361] All 2 monitor threads for this command have been started.
[21:20:06.361] Now waiting for 2 monitored axis/axes of this command to complete...
[21:20:06.361] Waiting for motor_x to complete...
[21:20:06.414] motor_x: Initial status is RUNNING.
[21:20:06.465] motor_y: Initial status is RUNNING.
[21:20:07.677] motor_x completed.
[21:20:07.677] Waiting for motor_y to complete...
[21:20:07.734] motor_y completed.
[21:20:07.734] All monitored axes for this command instance completed.
[21:20:07.749] run_motor_commands finished.
[21:20:07.749] 移动操作成功完成! 新电机位置: X=102.138 (已更新), Y=147.555 (已更新), Z=0.000, CAM=0.000
ReqID:unknown 物体编号:1 - 步骤 3: Z轴下降到目标位置 (300.0mm)。类别'螃蟹'(ID:0) | 基础偏移: 50.0mm | 额外偏移: 0.0mm | 总偏移: 50.0mm

[21:20:07.749] ===== 准备移动 =====
[21:20:07.749] 请求目标位置: X=不变, Y=不变, Z=300.0, CAM=不变
[21:20:07.749] 调整后物理目标: X=172.13764682412148, Y=217.55545690655708
[21:20:07.749] 当前电机位置: X=102.138, Y=147.555, Z=0.000, CAM=0.000
[21:20:07.749] 使用的轴偏移值 (来自配置): X=70.000, Y=70.000
[21:20:07.749] 计算电机目标: X=102.138, Y=147.555, Z=300.000, CAM=0.000
[21:20:07.749] 计算移动距离: dX=+0.000, dY=+0.000, dZ=+300.000, dCAM=+0.000
[21:20:07.749] DO2状态: 2 (不变(2))
[21:20:07.749] =====================
[21:20:07.751] run_motor_commands invoked.

[21:20:07.751] ===== 全局速度设置 =====
[21:20:07.751] 最大速度限制: 600 RPM
[21:20:07.751] ========================

[21:20:07.751] ===== Z轴配置信息 =====
[21:20:07.751] Z轴导程: 21.0 mm/转
[21:20:07.751] Z轴设置速度: 400 RPM
[21:20:07.751] Z轴实际速度: 400 RPM
[21:20:07.751] Z轴最大加速度: 0.4 m/s²
[21:20:07.751] Z轴从站地址: 4
[21:20:07.751] ========================

--- 各轴运动时间估算 ---
全局最大速度限制: 600 RPM
motor_x: 不移动，时间为0
motor_y: 不移动，时间为0

计算motor_z轴，距离=300.0mm:

--- 运动时间估算详情 ---
导程: 21.0 mm/转
目标速度: 400 RPM = 6.6667 转/秒 = 0.1400 m/s
加速度: 0.4 m/s²
加速时间: 0.3500秒
减速时间: 0.3500秒
加速位移: 0.0245 m
减速位移: 0.0245 m
总位移: 0.3000 m
情况: 达到最高速度，梯形速度曲线
加速时间: 0.3500秒
匀速距离: 0.2510m
匀速时间: 1.7929秒
减速时间: 0.3500秒
总估算时间: 2.4929秒 (加速+匀速+减速)
----------------------
motor_z估算时间: 2.493秒
motor_cam: 不移动，时间为0

--- 同步移动时间计算 ---
X轴时间: 0.000秒
Y轴时间: 0.000秒
Z轴时间: 2.493秒
同步移动时间: 2.493秒
Z轴: 应用同步速度补偿系数 1.70。速度从 400.0 RPM 调整为 680.0 RPM。
Z轴: 补偿后速度 680.0 RPM 超过有效最大限制 400.0 RPM，将使用限制值。
motor_z轴: 调整速度为 400.0 RPM 以达到同步时间 2.493秒

所有轴时间结果:
 - motor_x: 0.000秒
 - motor_y: 0.000秒
 - motor_z: 2.493秒
 - motor_cam: 0.000秒
Z轴时间: 2.493秒
最长运动时间: 2.493秒
同步移动时间: 2.493秒

DEBUG - 最终调整后的速度值:
motor_x: 150.000 RPM
motor_y: 180.000 RPM
motor_z: 400.000 RPM
motor_cam: 200.000 RPM
------------------------

[21:20:07.756] ===== 运动时间估算 =====
[21:20:07.756] Z轴移动距离: 300.0 mm
[21:20:07.756] 估算Z轴运动时间: 2.493 秒
[21:20:07.756] 所有轴中最长运动时间: 2.493 秒
[21:20:07.756] X、Y、Z轴同步移动时间: 2.493 秒
[21:20:07.756] =======================

[21:20:07.756] Pre-Z delay: 0.04s, then opening DO1 brake.
[21:20:07.827] Sending Z command: 300.000 mm, Speed: 400.0 RPM

--- 运动时间估算详情 ---
导程: 21.0 mm/转
目标速度: 400 RPM = 6.6667 转/秒 = 0.1400 m/s
加速度: 0.4 m/s²
加速时间: 0.3500秒
减速时间: 0.3500秒
加速位移: 0.0245 m
减速位移: 0.0245 m
总位移: 0.3000 m
情况: 达到最高速度，梯形速度曲线
加速时间: 0.3500秒
匀速距离: 0.2510m
匀速时间: 1.7929秒
减速时间: 0.3500秒
总估算时间: 2.4929秒 (加速+匀速+减速)
----------------------
[21:20:07.891] Starting 1 monitor thread(s) for this command...
[21:20:07.891] Z-Axis Monitor (Addr: 4): Started.
[21:20:07.891] All 1 monitor threads for this command have been started.
[21:20:07.891] Now waiting for 1 monitored axis/axes of this command to complete...
[21:20:07.891] Waiting for motor_z to complete...
[21:20:10.577] Z-axis (Addr: 4) status: 电机停止 (Status: 0x0003). Activating brake.
[21:20:10.598] Z-Axis Monitor (Addr: 4): Ended (normal or error).
[21:20:10.618] motor_z completed.
[21:20:10.636] All monitored axes for this command instance completed.
[21:20:10.636] Post-Z delay: 0.0s, then closing DO1 brake.
[21:20:10.636] Ensuring DO1 brake is closed.
[21:20:10.658] run_motor_commands finished.
[21:20:10.669] 移动操作成功完成! 新电机位置: X=102.138, Y=147.555, Z=300.000 (已更新), CAM=0.000
ReqID:unknown 物体编号:1 - 步骤 4: 闭合爪子抓取(DO2=0)

[21:20:10.669] ===== 准备移动 =====
[21:20:10.669] 请求目标位置: X=不变, Y=不变, Z=不变, CAM=不变
[21:20:10.669] 调整后物理目标: X=172.13764682412148, Y=217.55545690655708
[21:20:10.669] 当前电机位置: X=102.138, Y=147.555, Z=300.000, CAM=0.000
[21:20:10.669] 使用的轴偏移值 (来自配置): X=70.000, Y=70.000
[21:20:10.669] 计算电机目标: X=102.138, Y=147.555, Z=300.000, CAM=0.000
[21:20:10.669] 计算移动距离: dX=+0.000, dY=+0.000, dZ=+0.000, dCAM=+0.000
[21:20:10.669] DO2状态: 0 (抓取(0))
[21:20:10.669] =====================
[21:20:10.670] run_motor_commands invoked.

[21:20:10.670] ===== 全局速度设置 =====
[21:20:10.670] 最大速度限制: 600 RPM
[21:20:10.670] ========================

[21:20:10.670] ===== Z轴配置信息 =====
[21:20:10.670] Z轴导程: 21.0 mm/转
[21:20:10.670] Z轴设置速度: 400 RPM
[21:20:10.670] Z轴实际速度: 400 RPM
[21:20:10.670] Z轴最大加速度: 0.4 m/s²
[21:20:10.670] Z轴从站地址: 4
[21:20:10.670] ========================

--- 各轴运动时间估算 ---
全局最大速度限制: 600 RPM
motor_x: 不移动，时间为0
motor_y: 不移动，时间为0
motor_z: 不移动，时间为0
motor_cam: 不移动，时间为0

--- 同步移动时间计算 ---
X轴时间: 0.000秒
Y轴时间: 0.000秒
Z轴时间: 0.000秒
同步移动时间: 0.000秒

所有轴时间结果:
 - motor_x: 0.000秒
 - motor_y: 0.000秒
 - motor_z: 0.000秒
 - motor_cam: 0.000秒
Z轴时间: 0.000秒
最长运动时间: 0.000秒
同步移动时间: 0.000秒

DEBUG - 最终调整后的速度值:
motor_x: 150.000 RPM
motor_y: 180.000 RPM
motor_z: 400.000 RPM
motor_cam: 200.000 RPM
------------------------

[21:20:10.673] ===== 运动时间估算 =====
[21:20:10.673] Z轴移动距离: 0.0 mm
[21:20:10.673] 估算Z轴运动时间: 0.000 秒
[21:20:10.673] 所有轴中最长运动时间: 0.000 秒
[21:20:10.673] X、Y、Z轴同步移动时间: 0.000 秒
[21:20:10.673] =======================

[21:20:10.673] Controlling DO2 to: OFF
[21:20:10.700] No monitor threads to start for this command.
[21:20:10.700] No axes being monitored for completion in this command.
[21:20:10.700] All monitored axes for this command instance completed.
[21:20:10.700] run_motor_commands finished.
[21:20:10.700] 移动操作成功完成! 新电机位置: X=102.138, Y=147.555, Z=300.000, CAM=0.000
ReqID:unknown 物体编号:1 - 步骤 5: 等待抓取稳定时间 0.5秒
ReqID:unknown 物体编号:1 - 步骤 6: Z轴上升到临时高度 (50.0mm)

[21:20:11.201] ===== 准备移动 =====
[21:20:11.201] 请求目标位置: X=不变, Y=不变, Z=50.0, CAM=不变
[21:20:11.201] 调整后物理目标: X=172.13764682412148, Y=217.55545690655708
[21:20:11.201] 当前电机位置: X=102.138, Y=147.555, Z=300.000, CAM=0.000
[21:20:11.201] 使用的轴偏移值 (来自配置): X=70.000, Y=70.000
[21:20:11.201] 计算电机目标: X=102.138, Y=147.555, Z=50.000, CAM=0.000
[21:20:11.201] 计算移动距离: dX=+0.000, dY=+0.000, dZ=-250.000, dCAM=+0.000
[21:20:11.201] DO2状态: 2 (不变(2))
[21:20:11.201] =====================
[21:20:11.203] run_motor_commands invoked.

[21:20:11.203] ===== 全局速度设置 =====
[21:20:11.203] 最大速度限制: 600 RPM
[21:20:11.203] ========================

[21:20:11.203] ===== Z轴配置信息 =====
[21:20:11.203] Z轴导程: 21.0 mm/转
[21:20:11.203] Z轴设置速度: 400 RPM
[21:20:11.203] Z轴实际速度: 400 RPM
[21:20:11.203] Z轴最大加速度: 0.4 m/s²
[21:20:11.203] Z轴从站地址: 4
[21:20:11.203] ========================

--- 各轴运动时间估算 ---
全局最大速度限制: 600 RPM
motor_x: 不移动，时间为0
motor_y: 不移动，时间为0

计算motor_z轴，距离=-250.0mm:

--- 运动时间估算详情 ---
导程: 21.0 mm/转
目标速度: 400 RPM = 6.6667 转/秒 = 0.1400 m/s
加速度: 0.4 m/s²
加速时间: 0.3500秒
减速时间: 0.3500秒
加速位移: 0.0245 m
减速位移: 0.0245 m
总位移: 0.2500 m
情况: 达到最高速度，梯形速度曲线
加速时间: 0.3500秒
匀速距离: 0.2010m
匀速时间: 1.4357秒
减速时间: 0.3500秒
总估算时间: 2.1357秒 (加速+匀速+减速)
----------------------
motor_z估算时间: 2.136秒
motor_cam: 不移动，时间为0

--- 同步移动时间计算 ---
X轴时间: 0.000秒
Y轴时间: 0.000秒
Z轴时间: 2.136秒
同步移动时间: 2.136秒
Z轴: 应用同步速度补偿系数 1.70。速度从 400.0 RPM 调整为 680.0 RPM。
Z轴: 补偿后速度 680.0 RPM 超过有效最大限制 400.0 RPM，将使用限制值。
motor_z轴: 调整速度为 400.0 RPM 以达到同步时间 2.136秒

所有轴时间结果:
 - motor_x: 0.000秒
 - motor_y: 0.000秒
 - motor_z: 2.136秒
 - motor_cam: 0.000秒
Z轴时间: 2.136秒
最长运动时间: 2.136秒
同步移动时间: 2.136秒

DEBUG - 最终调整后的速度值:
motor_x: 150.000 RPM
motor_y: 180.000 RPM
motor_z: 400.000 RPM
motor_cam: 200.000 RPM
------------------------

[21:20:11.204] ===== 运动时间估算 =====
[21:20:11.204] Z轴移动距离: -250.0 mm
[21:20:11.204] 估算Z轴运动时间: 2.136 秒
[21:20:11.204] 所有轴中最长运动时间: 2.136 秒
[21:20:11.204] X、Y、Z轴同步移动时间: 2.136 秒
[21:20:11.204] =======================

[21:20:11.204] Pre-Z delay: 0.04s, then opening DO1 brake.
[21:20:11.271] Sending Z command: -250.000 mm, Speed: 400.0 RPM

--- 运动时间估算详情 ---
导程: 21.0 mm/转
目标速度: 400 RPM = 6.6667 转/秒 = 0.1400 m/s
加速度: 0.4 m/s²
加速时间: 0.3500秒
减速时间: 0.3500秒
加速位移: 0.0245 m
减速位移: 0.0245 m
总位移: 0.2500 m
情况: 达到最高速度，梯形速度曲线
加速时间: 0.3500秒
匀速距离: 0.2010m
匀速时间: 1.4357秒
减速时间: 0.3500秒
总估算时间: 2.1357秒 (加速+匀速+减速)
----------------------
[21:20:11.329] Starting 1 monitor thread(s) for this command...
[21:20:11.329] Z-Axis Monitor (Addr: 4): Started.
[21:20:11.329] All 1 monitor threads for this command have been started.
[21:20:11.329] Now waiting for 1 monitored axis/axes of this command to complete...
[21:20:11.329] Waiting for motor_z to complete...
[21:20:13.650] Z-axis (Addr: 4) status: 电机停止 (Status: 0x0002). Activating brake.
[21:20:13.671] Z-Axis Monitor (Addr: 4): Ended (normal or error).
[21:20:13.704] motor_z completed.
[21:20:13.722] All monitored axes for this command instance completed.
[21:20:13.723] Post-Z delay: 0.0s, then closing DO1 brake.
[21:20:13.723] Ensuring DO1 brake is closed.
[21:20:13.744] run_motor_commands finished.
[21:20:13.744] 移动操作成功完成! 新电机位置: X=102.138, Y=147.555, Z=50.000 (已更新), CAM=0.000
ReqID:unknown 物体编号:1 - 步骤 7: 检查爪子状态，判断是否抓取到物体
ReqID:unknown 物体编号:1 - 未检测到物体，可能抓取失败
ReqID:unknown 物体编号:1 - 步骤 8: 打开爪子(DO2=1) - 抓取失败路径

[21:20:13.804] ===== 准备移动 =====
[21:20:13.804] 请求目标位置: X=不变, Y=不变, Z=不变, CAM=不变
[21:20:13.804] 调整后物理目标: X=172.13764682412148, Y=217.55545690655708
[21:20:13.804] 当前电机位置: X=102.138, Y=147.555, Z=50.000, CAM=0.000
[21:20:13.804] 使用的轴偏移值 (来自配置): X=70.000, Y=70.000
[21:20:13.804] 计算电机目标: X=102.138, Y=147.555, Z=50.000, CAM=0.000
[21:20:13.804] 计算移动距离: dX=+0.000, dY=+0.000, dZ=+0.000, dCAM=+0.000
[21:20:13.804] DO2状态: 1 (释放(1))
[21:20:13.804] =====================
[21:20:13.804] run_motor_commands invoked.

[21:20:13.804] ===== 全局速度设置 =====
[21:20:13.804] 最大速度限制: 600 RPM
[21:20:13.804] ========================

[21:20:13.804] ===== Z轴配置信息 =====
[21:20:13.804] Z轴导程: 21.0 mm/转
[21:20:13.804] Z轴设置速度: 400 RPM
[21:20:13.804] Z轴实际速度: 400 RPM
[21:20:13.804] Z轴最大加速度: 0.4 m/s²
[21:20:13.804] Z轴从站地址: 4
[21:20:13.804] ========================

--- 各轴运动时间估算 ---
全局最大速度限制: 600 RPM
motor_x: 不移动，时间为0
motor_y: 不移动，时间为0
motor_z: 不移动，时间为0
motor_cam: 不移动，时间为0

--- 同步移动时间计算 ---
X轴时间: 0.000秒
Y轴时间: 0.000秒
Z轴时间: 0.000秒
同步移动时间: 0.000秒

所有轴时间结果:
 - motor_x: 0.000秒
 - motor_y: 0.000秒
 - motor_z: 0.000秒
 - motor_cam: 0.000秒
Z轴时间: 0.000秒
最长运动时间: 0.000秒
同步移动时间: 0.000秒

DEBUG - 最终调整后的速度值:
motor_x: 150.000 RPM
motor_y: 180.000 RPM
motor_z: 400.000 RPM
motor_cam: 200.000 RPM
------------------------

[21:20:13.804] ===== 运动时间估算 =====
[21:20:13.804] Z轴移动距离: 0.0 mm
[21:20:13.804] 估算Z轴运动时间: 0.000 秒
[21:20:13.804] 所有轴中最长运动时间: 0.000 秒
[21:20:13.804] X、Y、Z轴同步移动时间: 0.000 秒
[21:20:13.804] =======================

[21:20:13.804] Controlling DO2 to: ON
[21:20:13.834] No monitor threads to start for this command.
[21:20:13.834] No axes being monitored for completion in this command.
[21:20:13.866] All monitored axes for this command instance completed.
[21:20:13.866] run_motor_commands finished.
[21:20:13.866] 移动操作成功完成! 新电机位置: X=102.138, Y=147.555, Z=50.000, CAM=0.000
ReqID:unknown 物体编号:1 - 步骤 9: 移动到展示位置 (600.0, 342.0, 0.0) - 抓取失败路径

[21:20:13.867] ===== 准备移动 =====
[21:20:13.867] 请求目标位置: X=600.0, Y=342.0, Z=0.0, CAM=不变
[21:20:13.867] 调整后物理目标: X=600.0, Y=342.0
[21:20:13.867] 当前电机位置: X=102.138, Y=147.555, Z=50.000, CAM=0.000
[21:20:13.867] 使用的轴偏移值 (来自配置): X=70.000, Y=70.000
[21:20:13.867] 计算电机目标: X=530.000, Y=272.000, Z=0.000, CAM=0.000
[21:20:13.867] 计算移动距离: dX=+427.862, dY=+124.445, dZ=-50.000, dCAM=+0.000
[21:20:13.867] DO2状态: 2 (不变(2))
[21:20:13.867] =====================
[21:20:13.868] run_motor_commands invoked.

[21:20:13.868] ===== 全局速度设置 =====
[21:20:13.868] 最大速度限制: 600 RPM
[21:20:13.868] ========================

[21:20:13.868] ===== Z轴配置信息 =====
[21:20:13.868] Z轴导程: 21.0 mm/转
[21:20:13.868] Z轴设置速度: 400 RPM
[21:20:13.868] Z轴实际速度: 400 RPM
[21:20:13.868] Z轴最大加速度: 0.4 m/s²
[21:20:13.868] Z轴从站地址: 4
[21:20:13.868] ========================

--- 各轴运动时间估算 ---
全局最大速度限制: 600 RPM

计算motor_x轴，距离=427.8623531758785mm:

--- 运动时间估算详情 ---
导程: 105.0 mm/转
目标速度: 150 RPM = 2.5000 转/秒 = 0.2625 m/s
加速度: 0.6 m/s²
加速时间: 0.4375秒
减速时间: 0.4375秒
加速位移: 0.0574 m
减速位移: 0.0574 m
总位移: 0.4279 m
情况: 达到最高速度，梯形速度曲线
加速时间: 0.4375秒
匀速距离: 0.3130m
匀速时间: 1.1925秒
减速时间: 0.4375秒
总估算时间: 2.0675秒 (加速+匀速+减速)
----------------------
motor_x估算时间: 2.067秒

计算motor_y轴，距离=124.44454309344292mm:

--- 运动时间估算详情 ---
导程: 75.0 mm/转
目标速度: 180 RPM = 3.0000 转/秒 = 0.2250 m/s
加速度: 0.5 m/s²
加速时间: 0.4500秒
减速时间: 0.4500秒
加速位移: 0.0506 m
减速位移: 0.0506 m
总位移: 0.1244 m
情况: 达到最高速度，梯形速度曲线
加速时间: 0.4500秒
匀速距离: 0.0232m
匀速时间: 0.1031秒
减速时间: 0.4500秒
总估算时间: 1.0031秒 (加速+匀速+减速)
----------------------
motor_y估算时间: 1.003秒

计算motor_z轴，距离=-50.0mm:

--- 运动时间估算详情 ---
导程: 21.0 mm/转
目标速度: 400 RPM = 6.6667 转/秒 = 0.1400 m/s
加速度: 0.4 m/s²
加速时间: 0.3500秒
减速时间: 0.3500秒
加速位移: 0.0245 m
减速位移: 0.0245 m
总位移: 0.0500 m
情况: 达到最高速度，梯形速度曲线
加速时间: 0.3500秒
匀速距离: 0.0010m
匀速时间: 0.0071秒
减速时间: 0.3500秒
总估算时间: 0.7071秒 (加速+匀速+减速)
----------------------
motor_z估算时间: 0.707秒
motor_cam: 不移动，时间为0

--- 同步移动时间计算 ---
X轴时间: 2.067秒
Y轴时间: 1.003秒
Z轴时间: 0.707秒
同步移动时间: 2.067秒
motor_x轴: 计算同步速度 150.0 RPM 超过有效最大限制 150.0 RPM，将使用限制值。
motor_x轴: 调整速度为 150.0 RPM 以达到同步时间 2.067秒
motor_y轴: 调整速度为 51.3 RPM 以达到同步时间 2.067秒
Z轴: 应用同步速度补偿系数 1.70。速度从 71.2 RPM 调整为 121.1 RPM。
motor_z轴: 调整速度为 121.1 RPM 以达到同步时间 2.067秒

所有轴时间结果:
 - motor_x: 2.067秒
 - motor_y: 1.003秒
 - motor_z: 0.707秒
 - motor_cam: 0.000秒
Z轴时间: 0.707秒
最长运动时间: 2.067秒
同步移动时间: 2.067秒

DEBUG - 最终调整后的速度值:
motor_x: 150.000 RPM
motor_y: 51.341 RPM
motor_z: 121.119 RPM
motor_cam: 200.000 RPM
------------------------

[21:20:13.875] ===== 运动时间估算 =====
[21:20:13.875] Z轴移动距离: -50.0 mm
[21:20:13.875] 估算Z轴运动时间: 0.707 秒
[21:20:13.875] 所有轴中最长运动时间: 2.067 秒
[21:20:13.875] X、Y、Z轴同步移动时间: 2.067 秒
[21:20:13.875] =======================

[21:20:13.875] Pre-Z delay: 0.04s, then opening DO1 brake.
[21:20:13.939] Sending X command: 427.862 mm, Speed: 150.0 RPM

--- 运动时间估算详情 ---
导程: 105.0 mm/转
目标速度: 150 RPM = 2.5000 转/秒 = 0.2625 m/s
加速度: 0.6 m/s²
加速时间: 0.4375秒
减速时间: 0.4375秒
加速位移: 0.0574 m
减速位移: 0.0574 m
总位移: 0.4279 m
情况: 达到最高速度，梯形速度曲线
加速时间: 0.4375秒
匀速距离: 0.3130m
匀速时间: 1.1925秒
减速时间: 0.4375秒
总估算时间: 2.0675秒 (加速+匀速+减速)
----------------------
[21:20:13.995] Sending Y command: 124.445 mm, Speed: 51.3 RPM

--- 运动时间估算详情 ---
导程: 75.0 mm/转
目标速度: 51 RPM = 0.8500 转/秒 = 0.0638 m/s
加速度: 0.5 m/s²
加速时间: 0.1275秒
减速时间: 0.1275秒
加速位移: 0.0041 m
减速位移: 0.0041 m
总位移: 0.1244 m
情况: 达到最高速度，梯形速度曲线
加速时间: 0.1275秒
匀速距离: 0.1163m
匀速时间: 1.8246秒
减速时间: 0.1275秒
总估算时间: 2.0796秒 (加速+匀速+减速)
----------------------
[21:20:14.049] Sending Z command: -50.000 mm, Speed: 121.1 RPM

--- 运动时间估算详情 ---
导程: 21.0 mm/转
目标速度: 121 RPM = 2.0167 转/秒 = 0.0423 m/s
加速度: 0.4 m/s²
加速时间: 0.1059秒
减速时间: 0.1059秒
加速位移: 0.0022 m
减速位移: 0.0022 m
总位移: 0.0500 m
情况: 达到最高速度，梯形速度曲线
加速时间: 0.1059秒
匀速距离: 0.0455m
匀速时间: 1.0748秒
减速时间: 0.1059秒
总估算时间: 1.2865秒 (加速+匀速+减速)
----------------------
[21:20:14.121] Starting 3 monitor thread(s) for this command...
[21:20:14.121] motor_x (Addr: 3): Monitoring started.
[21:20:14.122] motor_y (Addr: 2): Monitoring started.
[21:20:14.122] Z-Axis Monitor (Addr: 4): Started.
[21:20:14.122] All 3 monitor threads for this command have been started.
[21:20:14.122] Now waiting for 3 monitored axis/axes of this command to complete...
[21:20:14.123] Waiting for motor_x to complete...
[21:20:14.173] motor_x: Initial status is RUNNING.
[21:20:14.223] motor_y: Initial status is RUNNING.
[21:20:15.494] Z-axis (Addr: 4) status: 电机停止 (Status: 0x0002). Activating brake.
[21:20:15.617] Z-Axis Monitor (Addr: 4): Ended (normal or error).
[21:20:16.240] motor_x completed.
[21:20:16.240] Waiting for motor_y to complete...
[21:20:16.295] motor_y completed.
[21:20:16.295] Waiting for motor_z to complete...
[21:20:16.300] motor_z completed.
[21:20:16.300] All monitored axes for this command instance completed.
[21:20:16.300] Post-Z delay: 0.0s, then closing DO1 brake.
[21:20:16.300] Ensuring DO1 brake is closed.
[21:20:16.333] run_motor_commands finished.
[21:20:16.339] 移动操作成功完成! 新电机位置: X=530.000 (已更新), Y=272.000 (已更新), Z=0.000 (已更新), CAM=0.000
ReqID:unknown 物体编号:1 - 步骤 10: 展示位置停留 1.0秒 - 抓取失败路径
ReqID:unknown 物体编号:1 - 步骤 11: 回到初始位置 - 抓取失败路径

[21:20:17.340] ===== 准备移动 =====
[21:20:17.340] 请求目标位置: X=70.0, Y=70.0, Z=0.0, CAM=不变
[21:20:17.340] 调整后物理目标: X=70.0, Y=70.0
[21:20:17.340] 当前电机位置: X=530.000, Y=272.000, Z=0.000, CAM=0.000
[21:20:17.340] 使用的轴偏移值 (来自配置): X=70.000, Y=70.000
[21:20:17.340] 计算电机目标: X=0.000, Y=0.000, Z=0.000, CAM=0.000
[21:20:17.340] 计算移动距离: dX=-530.000, dY=-272.000, dZ=+0.000, dCAM=+0.000
[21:20:17.340] DO2状态: 2 (不变(2))
[21:20:17.340] =====================
[21:20:17.340] run_motor_commands invoked.

[21:20:17.340] ===== 全局速度设置 =====
[21:20:17.340] 最大速度限制: 600 RPM
[21:20:17.340] ========================

[21:20:17.340] ===== Z轴配置信息 =====
[21:20:17.340] Z轴导程: 21.0 mm/转
[21:20:17.340] Z轴设置速度: 400 RPM
[21:20:17.340] Z轴实际速度: 400 RPM
[21:20:17.340] Z轴最大加速度: 0.4 m/s²
[21:20:17.340] Z轴从站地址: 4
[21:20:17.340] ========================

--- 各轴运动时间估算 ---
全局最大速度限制: 600 RPM

计算motor_x轴，距离=-530.0mm:

--- 运动时间估算详情 ---
导程: 105.0 mm/转
目标速度: 150 RPM = 2.5000 转/秒 = 0.2625 m/s
加速度: 0.6 m/s²
加速时间: 0.4375秒
减速时间: 0.4375秒
加速位移: 0.0574 m
减速位移: 0.0574 m
总位移: 0.5300 m
情况: 达到最高速度，梯形速度曲线
加速时间: 0.4375秒
匀速距离: 0.4152m
匀速时间: 1.5815秒
减速时间: 0.4375秒
总估算时间: 2.4565秒 (加速+匀速+减速)
----------------------
motor_x估算时间: 2.457秒

计算motor_y轴，距离=-272.0mm:

--- 运动时间估算详情 ---
导程: 75.0 mm/转
目标速度: 180 RPM = 3.0000 转/秒 = 0.2250 m/s
加速度: 0.5 m/s²
加速时间: 0.4500秒
减速时间: 0.4500秒
加速位移: 0.0506 m
减速位移: 0.0506 m
总位移: 0.2720 m
情况: 达到最高速度，梯形速度曲线
加速时间: 0.4500秒
匀速距离: 0.1708m
匀速时间: 0.7589秒
减速时间: 0.4500秒
总估算时间: 1.6589秒 (加速+匀速+减速)
----------------------
motor_y估算时间: 1.659秒
motor_z: 不移动，时间为0
motor_cam: 不移动，时间为0

--- 同步移动时间计算 ---
X轴时间: 2.457秒
Y轴时间: 1.659秒
Z轴时间: 0.000秒
同步移动时间: 2.457秒
motor_x轴: 调整速度为 150.0 RPM 以达到同步时间 2.457秒
motor_y轴: 调整速度为 98.4 RPM 以达到同步时间 2.457秒

所有轴时间结果:
 - motor_x: 2.457秒
 - motor_y: 1.659秒
 - motor_z: 0.000秒
 - motor_cam: 0.000秒
Z轴时间: 0.000秒
最长运动时间: 2.457秒
同步移动时间: 2.457秒

DEBUG - 最终调整后的速度值:
motor_x: 150.000 RPM
motor_y: 98.442 RPM
motor_z: 400.000 RPM
motor_cam: 200.000 RPM
------------------------

[21:20:17.340] ===== 运动时间估算 =====
[21:20:17.340] Z轴移动距离: 0.0 mm
[21:20:17.340] 估算Z轴运动时间: 0.000 秒
[21:20:17.340] 所有轴中最长运动时间: 2.457 秒
[21:20:17.340] X、Y、Z轴同步移动时间: 2.457 秒
[21:20:17.340] =======================

[21:20:17.340] Sending X command: -530.000 mm, Speed: 150.0 RPM

--- 运动时间估算详情 ---
导程: 105.0 mm/转
目标速度: 150 RPM = 2.5000 转/秒 = 0.2625 m/s
加速度: 0.6 m/s²
加速时间: 0.4375秒
减速时间: 0.4375秒
加速位移: 0.0574 m
减速位移: 0.0574 m
总位移: 0.5300 m
情况: 达到最高速度，梯形速度曲线
加速时间: 0.4375秒
匀速距离: 0.4152m
匀速时间: 1.5815秒
减速时间: 0.4375秒
总估算时间: 2.4565秒 (加速+匀速+减速)
----------------------
[21:20:17.402] Sending Y command: -272.000 mm, Speed: 98.4 RPM

--- 运动时间估算详情 ---
导程: 75.0 mm/转
目标速度: 98 RPM = 1.6333 转/秒 = 0.1225 m/s
加速度: 0.5 m/s²
加速时间: 0.2450秒
减速时间: 0.2450秒
加速位移: 0.0150 m
减速位移: 0.0150 m
总位移: 0.2720 m
情况: 达到最高速度，梯形速度曲线
加速时间: 0.2450秒
匀速距离: 0.2420m
匀速时间: 1.9754秒
减速时间: 0.2450秒
总估算时间: 2.4654秒 (加速+匀速+减速)
----------------------
[21:20:17.466] Starting 2 monitor thread(s) for this command...
[21:20:17.467] motor_x (Addr: 3): Monitoring started.
[21:20:17.467] motor_y (Addr: 2): Monitoring started.
[21:20:17.467] All 2 monitor threads for this command have been started.
[21:20:17.467] Now waiting for 2 monitored axis/axes of this command to complete...
[21:20:17.468] Waiting for motor_x to complete...
[21:20:17.518] motor_x: Initial status is RUNNING.
[21:20:17.569] motor_y: Initial status is RUNNING.
[21:20:20.041] motor_x completed.
[21:20:20.041] Waiting for motor_y to complete...
[21:20:20.094] motor_y completed.
[21:20:20.094] All monitored axes for this command instance completed.
[21:20:20.113] run_motor_commands finished.
[21:20:20.113] 移动操作成功完成! 新电机位置: X=0.000 (已更新), Y=0.000 (已更新), Z=0.000 (已更新), CAM=0.000
ReqID:unknown 物体编号:1 - 未抓取到物体，本轮操作结束
[21:20:20.116] ReqID:unknown 物体编号:1 - 抓取周期函数 move_to_object 执行完毕，将触发配置重载。

[21:20:20.116] ===== 游戏周期结束，检查配置更新 =====
[21:20:20.117] 配置文件 Config_Move.yaml 未修改，跳过更新
[21:20:20.117] 配置文件未修改，无需更新
[21:20:20.117] ===== 配置检查流程结束 =====


当前检测到 2 个物体:
--------------------------------------------------------------------------------------
编号  |  类别ID      |  物理坐标(mm)   | 归一化比例 | 置信度 | 估算时间(秒)
--------------------------------------------------------------------------------------
1     | 0  (螃蟹    ) | (172.0, 218.2)  | (0.251, 0.318)     | 0.94   | N/A
3     | 1  (小龙虾   ) | (397.1, 469.7)  | (0.580, 0.686)     | 0.90   | N/A
--------------------------------------------------------------------------------------
输入物体编号移动到该位置，输入'r'刷新物体列表，输入'q'退出

请输入物体编号或命令(r=刷新, q=退出): r
刷新物体列表...

当前检测到 3 个物体:
--------------------------------------------------------------------------------------
编号  |  类别ID      |  物理坐标(mm)   | 归一化比例 | 置信度 | 估算时间(秒)
--------------------------------------------------------------------------------------
1     | 0  (螃蟹    ) | (172.4, 218.1)  | (0.252, 0.318)     | 0.94   | N/A
2     | 2  (贝壳    ) | (434.8, 170.7)  | (0.635, 0.249)     | 0.91   | N/A
3     | 1  (小龙虾   ) | (396.6, 470.4)  | (0.579, 0.687)     | 0.91   | N/A
--------------------------------------------------------------------------------------
输入物体编号移动到该位置，输入'r'刷新物体列表，输入'q'退出

请输入物体编号或命令(r=刷新, q=退出): 2

ReqID:unknown 物体编号:2 - 准备移动到物体 #2 (贝壳)
ReqID:unknown 物体编号:2 - 物理坐标: (434.8, 170.7) mm

ReqID:unknown 物体编号:2 - ===== 开始抓取流程 =====
ReqID:unknown 物体编号:2 - 步骤 1: 打开爪子(DO2=1)

[21:20:29.896] ===== 准备移动 =====
[21:20:29.896] 请求目标位置: X=不变, Y=不变, Z=不变, CAM=不变
[21:20:29.896] 调整后物理目标: X=70.0, Y=70.0
[21:20:29.896] 当前电机位置: X=0.000, Y=0.000, Z=0.000, CAM=0.000
[21:20:29.896] 使用的轴偏移值 (来自配置): X=70.000, Y=70.000
[21:20:29.896] 计算电机目标: X=0.000, Y=0.000, Z=0.000, CAM=0.000
[21:20:29.896] 计算移动距离: dX=+0.000, dY=+0.000, dZ=+0.000, dCAM=+0.000
[21:20:29.896] DO2状态: 1 (释放(1))
[21:20:29.896] =====================
[21:20:29.896] run_motor_commands invoked.

[21:20:29.896] ===== 全局速度设置 =====
[21:20:29.896] 最大速度限制: 600 RPM
[21:20:29.896] ========================

[21:20:29.896] ===== Z轴配置信息 =====
[21:20:29.896] Z轴导程: 21.0 mm/转
[21:20:29.896] Z轴设置速度: 400 RPM
[21:20:29.896] Z轴实际速度: 400 RPM
[21:20:29.896] Z轴最大加速度: 0.4 m/s²
[21:20:29.896] Z轴从站地址: 4
[21:20:29.896] ========================

--- 各轴运动时间估算 ---
全局最大速度限制: 600 RPM
motor_x: 不移动，时间为0
motor_y: 不移动，时间为0
motor_z: 不移动，时间为0
motor_cam: 不移动，时间为0

--- 同步移动时间计算 ---
X轴时间: 0.000秒
Y轴时间: 0.000秒
Z轴时间: 0.000秒
同步移动时间: 0.000秒

所有轴时间结果:
 - motor_x: 0.000秒
 - motor_y: 0.000秒
 - motor_z: 0.000秒
 - motor_cam: 0.000秒
Z轴时间: 0.000秒
最长运动时间: 0.000秒
同步移动时间: 0.000秒

DEBUG - 最终调整后的速度值:
motor_x: 150.000 RPM
motor_y: 180.000 RPM
motor_z: 400.000 RPM
motor_cam: 200.000 RPM
------------------------

[21:20:29.896] ===== 运动时间估算 =====
[21:20:29.896] Z轴移动距离: 0.0 mm
[21:20:29.896] 估算Z轴运动时间: 0.000 秒
[21:20:29.896] 所有轴中最长运动时间: 0.000 秒
[21:20:29.896] X、Y、Z轴同步移动时间: 0.000 秒
[21:20:29.896] =======================

[21:20:29.896] Controlling DO2 to: ON
[21:20:29.929] No monitor threads to start for this command.
[21:20:29.932] No axes being monitored for completion in this command.
[21:20:29.932] All monitored axes for this command instance completed.
[21:20:29.932] run_motor_commands finished.
[21:20:29.932] 移动操作成功完成! 新电机位置: X=0.000, Y=0.000, Z=0.000, CAM=0.000
ReqID:unknown 物体编号:2 - 步骤 2: 移动X、Y轴到目标位置 (434.8, 170.7)

[21:20:30.134] ===== 准备移动 =====
[21:20:30.134] 请求目标位置: X=434.8011836409569, Y=170.72450801730156, Z=不变, CAM=不变
[21:20:30.134] 调整后物理目标: X=434.8011836409569, Y=170.72450801730156
[21:20:30.134] 当前电机位置: X=0.000, Y=0.000, Z=0.000, CAM=0.000
[21:20:30.134] 使用的轴偏移值 (来自配置): X=70.000, Y=70.000
[21:20:30.134] 计算电机目标: X=364.801, Y=100.725, Z=0.000, CAM=0.000
[21:20:30.134] 计算移动距离: dX=+364.801, dY=+100.725, dZ=+0.000, dCAM=+0.000
[21:20:30.134] DO2状态: 2 (不变(2))
[21:20:30.134] =====================
[21:20:30.134] run_motor_commands invoked.

[21:20:30.134] ===== 全局速度设置 =====
[21:20:30.134] 最大速度限制: 600 RPM
[21:20:30.134] ========================

[21:20:30.134] ===== Z轴配置信息 =====
[21:20:30.134] Z轴导程: 21.0 mm/转
[21:20:30.134] Z轴设置速度: 400 RPM
[21:20:30.134] Z轴实际速度: 400 RPM
[21:20:30.134] Z轴最大加速度: 0.4 m/s²
[21:20:30.134] Z轴从站地址: 4
[21:20:30.134] ========================

--- 各轴运动时间估算 ---
全局最大速度限制: 600 RPM

计算motor_x轴，距离=364.8011836409569mm:

--- 运动时间估算详情 ---
导程: 105.0 mm/转
目标速度: 150 RPM = 2.5000 转/秒 = 0.2625 m/s
加速度: 0.6 m/s²
加速时间: 0.4375秒
减速时间: 0.4375秒
加速位移: 0.0574 m
减速位移: 0.0574 m
总位移: 0.3648 m
情况: 达到最高速度，梯形速度曲线
加速时间: 0.4375秒
匀速距离: 0.2500m
匀速时间: 0.9522秒
减速时间: 0.4375秒
总估算时间: 1.8272秒 (加速+匀速+减速)
----------------------
motor_x估算时间: 1.827秒

计算motor_y轴，距离=100.72450801730156mm:

--- 运动时间估算详情 ---
导程: 75.0 mm/转
目标速度: 180 RPM = 3.0000 转/秒 = 0.2250 m/s
加速度: 0.5 m/s²
加速时间: 0.4500秒
减速时间: 0.4500秒
加速位移: 0.0506 m
减速位移: 0.0506 m
总位移: 0.1007 m
情况: 未达到最高速度，三角形速度曲线
峰值速度: 0.3174 m/s = 253.9 RPM
调整后加减速时间: 0.6347秒 x 2
估算时间: 1.2695秒
----------------------
motor_y估算时间: 1.269秒
motor_z: 不移动，时间为0
motor_cam: 不移动，时间为0

--- 同步移动时间计算 ---
X轴时间: 1.827秒
Y轴时间: 1.269秒
Z轴时间: 0.000秒
同步移动时间: 1.827秒
motor_x轴: 计算同步速度 150.0 RPM 超过有效最大限制 150.0 RPM，将使用限制值。
motor_x轴: 调整速度为 150.0 RPM 以达到同步时间 1.827秒
motor_y轴: 调整速度为 47.1 RPM 以达到同步时间 1.827秒

所有轴时间结果:
 - motor_x: 1.827秒
 - motor_y: 1.269秒
 - motor_z: 0.000秒
 - motor_cam: 0.000秒
Z轴时间: 0.000秒
最长运动时间: 1.827秒
同步移动时间: 1.827秒

DEBUG - 最终调整后的速度值:
motor_x: 150.000 RPM
motor_y: 47.140 RPM
motor_z: 400.000 RPM
motor_cam: 200.000 RPM
------------------------

[21:20:30.134] ===== 运动时间估算 =====
[21:20:30.134] Z轴移动距离: 0.0 mm
[21:20:30.134] 估算Z轴运动时间: 0.000 秒
[21:20:30.134] 所有轴中最长运动时间: 1.827 秒
[21:20:30.134] X、Y、Z轴同步移动时间: 1.827 秒
[21:20:30.134] =======================

[21:20:30.143] Sending X command: 364.801 mm, Speed: 150.0 RPM

--- 运动时间估算详情 ---
导程: 105.0 mm/转
目标速度: 150 RPM = 2.5000 转/秒 = 0.2625 m/s
加速度: 0.6 m/s²
加速时间: 0.4375秒
减速时间: 0.4375秒
加速位移: 0.0574 m
减速位移: 0.0574 m
总位移: 0.3648 m
情况: 达到最高速度，梯形速度曲线
加速时间: 0.4375秒
匀速距离: 0.2500m
匀速时间: 0.9522秒
减速时间: 0.4375秒
总估算时间: 1.8272秒 (加速+匀速+减速)
----------------------
[21:20:30.193] Sending Y command: 100.725 mm, Speed: 47.1 RPM

--- 运动时间估算详情 ---
导程: 75.0 mm/转
目标速度: 47 RPM = 0.7833 转/秒 = 0.0587 m/s
加速度: 0.5 m/s²
加速时间: 0.1175秒
减速时间: 0.1175秒
加速位移: 0.0035 m
减速位移: 0.0035 m
总位移: 0.1007 m
情况: 达到最高速度，梯形速度曲线
加速时间: 0.1175秒
匀速距离: 0.0938m
匀速时间: 1.5970秒
减速时间: 0.1175秒
总估算时间: 1.8320秒 (加速+匀速+减速)
----------------------
[21:20:30.248] Starting 2 monitor thread(s) for this command...
[21:20:30.249] motor_x (Addr: 3): Monitoring started.
[21:20:30.249] motor_y (Addr: 2): Monitoring started.
[21:20:30.249] All 2 monitor threads for this command have been started.
[21:20:30.250] Now waiting for 2 monitored axis/axes of this command to complete...
[21:20:30.250] Waiting for motor_x to complete...
[21:20:30.301] motor_x: Initial status is RUNNING.
[21:20:30.352] motor_y: Initial status is RUNNING.
[21:20:32.271] motor_x completed.
[21:20:32.271] Waiting for motor_y to complete...
[21:20:32.271] motor_y completed.
[21:20:32.271] All monitored axes for this command instance completed.
[21:20:32.271] run_motor_commands finished.
[21:20:32.271] 移动操作成功完成! 新电机位置: X=364.801 (已更新), Y=100.725 (已更新), Z=0.000, CAM=0.000
ReqID:unknown 物体编号:2 - 步骤 3: Z轴下降到目标位置 (345.0mm)。类别'贝壳'(ID:2) | 基础偏移: 5.0mm | 额外偏移: 0.0mm | 总偏移: 5.0mm

[21:20:32.271] ===== 准备移动 =====
[21:20:32.271] 请求目标位置: X=不变, Y=不变, Z=345.0, CAM=不变
[21:20:32.271] 调整后物理目标: X=434.8011836409569, Y=170.72450801730156
[21:20:32.271] 当前电机位置: X=364.801, Y=100.725, Z=0.000, CAM=0.000
[21:20:32.271] 使用的轴偏移值 (来自配置): X=70.000, Y=70.000
[21:20:32.271] 计算电机目标: X=364.801, Y=100.725, Z=345.000, CAM=0.000
[21:20:32.271] 计算移动距离: dX=+0.000, dY=+0.000, dZ=+345.000, dCAM=+0.000
[21:20:32.271] DO2状态: 2 (不变(2))
[21:20:32.271] =====================
[21:20:32.271] run_motor_commands invoked.

[21:20:32.271] ===== 全局速度设置 =====
[21:20:32.271] 最大速度限制: 600 RPM
[21:20:32.271] ========================

[21:20:32.271] ===== Z轴配置信息 =====
[21:20:32.271] Z轴导程: 21.0 mm/转
[21:20:32.271] Z轴设置速度: 400 RPM
[21:20:32.271] Z轴实际速度: 400 RPM
[21:20:32.271] Z轴最大加速度: 0.4 m/s²
[21:20:32.271] Z轴从站地址: 4
[21:20:32.271] ========================

--- 各轴运动时间估算 ---
全局最大速度限制: 600 RPM
motor_x: 不移动，时间为0
motor_y: 不移动，时间为0

计算motor_z轴，距离=345.0mm:

--- 运动时间估算详情 ---
导程: 21.0 mm/转
目标速度: 400 RPM = 6.6667 转/秒 = 0.1400 m/s
加速度: 0.4 m/s²
加速时间: 0.3500秒
减速时间: 0.3500秒
加速位移: 0.0245 m
减速位移: 0.0245 m
总位移: 0.3450 m
情况: 达到最高速度，梯形速度曲线
加速时间: 0.3500秒
匀速距离: 0.2960m
匀速时间: 2.1143秒
减速时间: 0.3500秒
总估算时间: 2.8143秒 (加速+匀速+减速)
----------------------
motor_z估算时间: 2.814秒
motor_cam: 不移动，时间为0

--- 同步移动时间计算 ---
X轴时间: 0.000秒
Y轴时间: 0.000秒
Z轴时间: 2.814秒
同步移动时间: 2.814秒
motor_z轴: 计算同步速度 400.0 RPM 超过有效最大限制 400.0 RPM，将使用限制值。
Z轴: 应用同步速度补偿系数 1.70。速度从 400.0 RPM 调整为 680.0 RPM。
Z轴: 补偿后速度 680.0 RPM 超过有效最大限制 400.0 RPM，将使用限制值。
motor_z轴: 调整速度为 400.0 RPM 以达到同步时间 2.814秒

所有轴时间结果:
 - motor_x: 0.000秒
 - motor_y: 0.000秒
 - motor_z: 2.814秒
 - motor_cam: 0.000秒
Z轴时间: 2.814秒
最长运动时间: 2.814秒
同步移动时间: 2.814秒

DEBUG - 最终调整后的速度值:
motor_x: 150.000 RPM
motor_y: 180.000 RPM
motor_z: 400.000 RPM
motor_cam: 200.000 RPM
------------------------

[21:20:32.271] ===== 运动时间估算 =====
[21:20:32.271] Z轴移动距离: 345.0 mm
[21:20:32.271] 估算Z轴运动时间: 2.814 秒
[21:20:32.271] 所有轴中最长运动时间: 2.814 秒
[21:20:32.271] X、Y、Z轴同步移动时间: 2.814 秒
[21:20:32.271] =======================

[21:20:32.271] Pre-Z delay: 0.04s, then opening DO1 brake.
[21:20:32.359] Sending Z command: 345.000 mm, Speed: 400.0 RPM

--- 运动时间估算详情 ---
导程: 21.0 mm/转
目标速度: 400 RPM = 6.6667 转/秒 = 0.1400 m/s
加速度: 0.4 m/s²
加速时间: 0.3500秒
减速时间: 0.3500秒
加速位移: 0.0245 m
减速位移: 0.0245 m
总位移: 0.3450 m
情况: 达到最高速度，梯形速度曲线
加速时间: 0.3500秒
匀速距离: 0.2960m
匀速时间: 2.1143秒
减速时间: 0.3500秒
总估算时间: 2.8143秒 (加速+匀速+减速)
----------------------
[21:20:32.409] Starting 1 monitor thread(s) for this command...
[21:20:32.410] Z-Axis Monitor (Addr: 4): Started.
[21:20:32.410] All 1 monitor threads for this command have been started.
[21:20:32.410] Now waiting for 1 monitored axis/axes of this command to complete...
[21:20:32.410] Waiting for motor_z to complete...
[21:20:35.659] Z-axis (Addr: 4) status: 电机停止 (Status: 0x0003). Activating brake.
[21:20:35.680] Z-Axis Monitor (Addr: 4): Ended (normal or error).
[21:20:35.688] motor_z completed.
[21:20:35.690] All monitored axes for this command instance completed.
[21:20:35.691] Post-Z delay: 0.0s, then closing DO1 brake.
[21:20:35.691] Ensuring DO1 brake is closed.
[21:20:35.712] run_motor_commands finished.
[21:20:35.712] 移动操作成功完成! 新电机位置: X=364.801, Y=100.725, Z=345.000 (已更新), CAM=0.000
ReqID:unknown 物体编号:2 - 步骤 4: 闭合爪子抓取(DO2=0)

[21:20:35.712] ===== 准备移动 =====
[21:20:35.712] 请求目标位置: X=不变, Y=不变, Z=不变, CAM=不变
[21:20:35.712] 调整后物理目标: X=434.8011836409569, Y=170.72450801730156
[21:20:35.712] 当前电机位置: X=364.801, Y=100.725, Z=345.000, CAM=0.000
[21:20:35.712] 使用的轴偏移值 (来自配置): X=70.000, Y=70.000
[21:20:35.712] 计算电机目标: X=364.801, Y=100.725, Z=345.000, CAM=0.000
[21:20:35.712] 计算移动距离: dX=+0.000, dY=+0.000, dZ=+0.000, dCAM=+0.000
[21:20:35.712] DO2状态: 0 (抓取(0))
[21:20:35.712] =====================
[21:20:35.712] run_motor_commands invoked.

[21:20:35.712] ===== 全局速度设置 =====
[21:20:35.712] 最大速度限制: 600 RPM
[21:20:35.712] ========================

[21:20:35.712] ===== Z轴配置信息 =====
[21:20:35.712] Z轴导程: 21.0 mm/转
[21:20:35.712] Z轴设置速度: 400 RPM
[21:20:35.712] Z轴实际速度: 400 RPM
[21:20:35.712] Z轴最大加速度: 0.4 m/s²
[21:20:35.712] Z轴从站地址: 4
[21:20:35.712] ========================

--- 各轴运动时间估算 ---
全局最大速度限制: 600 RPM
motor_x: 不移动，时间为0
motor_y: 不移动，时间为0
motor_z: 不移动，时间为0
motor_cam: 不移动，时间为0

--- 同步移动时间计算 ---
X轴时间: 0.000秒
Y轴时间: 0.000秒
Z轴时间: 0.000秒
同步移动时间: 0.000秒

所有轴时间结果:
 - motor_x: 0.000秒
 - motor_y: 0.000秒
 - motor_z: 0.000秒
 - motor_cam: 0.000秒
Z轴时间: 0.000秒
最长运动时间: 0.000秒
同步移动时间: 0.000秒

DEBUG - 最终调整后的速度值:
motor_x: 150.000 RPM
motor_y: 180.000 RPM
motor_z: 400.000 RPM
motor_cam: 200.000 RPM
------------------------

[21:20:35.712] ===== 运动时间估算 =====
[21:20:35.712] Z轴移动距离: 0.0 mm
[21:20:35.712] 估算Z轴运动时间: 0.000 秒
[21:20:35.712] 所有轴中最长运动时间: 0.000 秒
[21:20:35.712] X、Y、Z轴同步移动时间: 0.000 秒
[21:20:35.712] =======================

[21:20:35.712] Controlling DO2 to: OFF
[21:20:35.741] No monitor threads to start for this command.
[21:20:35.741] No axes being monitored for completion in this command.
[21:20:35.741] All monitored axes for this command instance completed.
[21:20:35.741] run_motor_commands finished.
[21:20:35.741] 移动操作成功完成! 新电机位置: X=364.801, Y=100.725, Z=345.000, CAM=0.000
ReqID:unknown 物体编号:2 - 步骤 5: 等待抓取稳定时间 0.5秒
ReqID:unknown 物体编号:2 - 步骤 6: Z轴上升到临时高度 (50.0mm)

[21:20:36.241] ===== 准备移动 =====
[21:20:36.241] 请求目标位置: X=不变, Y=不变, Z=50.0, CAM=不变
[21:20:36.241] 调整后物理目标: X=434.8011836409569, Y=170.72450801730156
[21:20:36.241] 当前电机位置: X=364.801, Y=100.725, Z=345.000, CAM=0.000
[21:20:36.241] 使用的轴偏移值 (来自配置): X=70.000, Y=70.000
[21:20:36.241] 计算电机目标: X=364.801, Y=100.725, Z=50.000, CAM=0.000
[21:20:36.241] 计算移动距离: dX=+0.000, dY=+0.000, dZ=-295.000, dCAM=+0.000
[21:20:36.241] DO2状态: 2 (不变(2))
[21:20:36.241] =====================
[21:20:36.241] run_motor_commands invoked.

[21:20:36.241] ===== 全局速度设置 =====
[21:20:36.241] 最大速度限制: 600 RPM
[21:20:36.241] ========================

[21:20:36.241] ===== Z轴配置信息 =====
[21:20:36.241] Z轴导程: 21.0 mm/转
[21:20:36.241] Z轴设置速度: 400 RPM
[21:20:36.241] Z轴实际速度: 400 RPM
[21:20:36.241] Z轴最大加速度: 0.4 m/s²
[21:20:36.241] Z轴从站地址: 4
[21:20:36.241] ========================

--- 各轴运动时间估算 ---
全局最大速度限制: 600 RPM
motor_x: 不移动，时间为0
motor_y: 不移动，时间为0

计算motor_z轴，距离=-295.0mm:

--- 运动时间估算详情 ---
导程: 21.0 mm/转
目标速度: 400 RPM = 6.6667 转/秒 = 0.1400 m/s
加速度: 0.4 m/s²
加速时间: 0.3500秒
减速时间: 0.3500秒
加速位移: 0.0245 m
减速位移: 0.0245 m
总位移: 0.2950 m
情况: 达到最高速度，梯形速度曲线
加速时间: 0.3500秒
匀速距离: 0.2460m
匀速时间: 1.7571秒
减速时间: 0.3500秒
总估算时间: 2.4571秒 (加速+匀速+减速)
----------------------
motor_z估算时间: 2.457秒
motor_cam: 不移动，时间为0

--- 同步移动时间计算 ---
X轴时间: 0.000秒
Y轴时间: 0.000秒
Z轴时间: 2.457秒
同步移动时间: 2.457秒
Z轴: 应用同步速度补偿系数 1.70。速度从 400.0 RPM 调整为 680.0 RPM。
Z轴: 补偿后速度 680.0 RPM 超过有效最大限制 400.0 RPM，将使用限制值。
motor_z轴: 调整速度为 400.0 RPM 以达到同步时间 2.457秒

所有轴时间结果:
 - motor_x: 0.000秒
 - motor_y: 0.000秒
 - motor_z: 2.457秒
 - motor_cam: 0.000秒
Z轴时间: 2.457秒
最长运动时间: 2.457秒
同步移动时间: 2.457秒

DEBUG - 最终调整后的速度值:
motor_x: 150.000 RPM
motor_y: 180.000 RPM
motor_z: 400.000 RPM
motor_cam: 200.000 RPM
------------------------

[21:20:36.241] ===== 运动时间估算 =====
[21:20:36.241] Z轴移动距离: -295.0 mm
[21:20:36.241] 估算Z轴运动时间: 2.457 秒
[21:20:36.241] 所有轴中最长运动时间: 2.457 秒
[21:20:36.241] X、Y、Z轴同步移动时间: 2.457 秒
[21:20:36.241] =======================

[21:20:36.241] Pre-Z delay: 0.04s, then opening DO1 brake.
[21:20:36.332] Sending Z command: -295.000 mm, Speed: 400.0 RPM

--- 运动时间估算详情 ---
导程: 21.0 mm/转
目标速度: 400 RPM = 6.6667 转/秒 = 0.1400 m/s
加速度: 0.4 m/s²
加速时间: 0.3500秒
减速时间: 0.3500秒
加速位移: 0.0245 m
减速位移: 0.0245 m
总位移: 0.2950 m
情况: 达到最高速度，梯形速度曲线
加速时间: 0.3500秒
匀速距离: 0.2460m
匀速时间: 1.7571秒
减速时间: 0.3500秒
总估算时间: 2.4571秒 (加速+匀速+减速)
----------------------
[21:20:36.383] Starting 1 monitor thread(s) for this command...
[21:20:36.383] Z-Axis Monitor (Addr: 4): Started.
[21:20:36.383] All 1 monitor threads for this command have been started.
[21:20:36.383] Now waiting for 1 monitored axis/axes of this command to complete...
[21:20:36.383] Waiting for motor_z to complete...
[21:20:39.001] Z-axis (Addr: 4) status: 电机停止 (Status: 0x0002). Activating brake.
[21:20:39.022] Z-Axis Monitor (Addr: 4): Ended (normal or error).
[21:20:39.066] motor_z completed.
[21:20:39.068] All monitored axes for this command instance completed.
[21:20:39.068] Post-Z delay: 0.0s, then closing DO1 brake.
[21:20:39.068] Ensuring DO1 brake is closed.
[21:20:39.090] run_motor_commands finished.
[21:20:39.090] 移动操作成功完成! 新电机位置: X=364.801, Y=100.725, Z=50.000 (已更新), CAM=0.000
ReqID:unknown 物体编号:2 - 步骤 7: 检查爪子状态，判断是否抓取到物体
ReqID:unknown 物体编号:2 - 成功抓取到物体!
ReqID:unknown 物体编号:2 - 步骤 8: 启动CAM轴并发展示线程。
[21:20:39.175] ReqID:unknown ObjID:2 CAMThread - CAM展示线程启动。
ReqID:unknown 物体编号:2 - 步骤 9: XYZ流程: 临时高度停留 0.05秒 (抓取成功后)
[21:20:39.176] ReqID:unknown ObjID:2 CAMThread - CAM展示: 等待 0.2 秒。
ReqID:unknown 物体编号:2 - 步骤 10: XYZ流程: 移动到展示位置 (600.0, 342.0, 0.0) (抓取成功后)

[21:20:39.226] ===== 准备移动 =====
[21:20:39.226] 请求目标位置: X=600.0, Y=342.0, Z=0.0, CAM=不变
[21:20:39.226] 调整后物理目标: X=600.0, Y=342.0
[21:20:39.226] 当前电机位置: X=364.801, Y=100.725, Z=50.000, CAM=0.000
[21:20:39.226] 使用的轴偏移值 (来自配置): X=70.000, Y=70.000
[21:20:39.226] 计算电机目标: X=530.000, Y=272.000, Z=0.000, CAM=0.000
[21:20:39.226] 计算移动距离: dX=+165.199, dY=+171.275, dZ=-50.000, dCAM=+0.000
[21:20:39.226] DO2状态: 2 (不变(2))
[21:20:39.226] =====================
[21:20:39.226] run_motor_commands invoked.

[21:20:39.226] ===== 全局速度设置 =====
[21:20:39.226] 最大速度限制: 600 RPM
[21:20:39.226] ========================

[21:20:39.226] ===== Z轴配置信息 =====
[21:20:39.226] Z轴导程: 21.0 mm/转
[21:20:39.226] Z轴设置速度: 400 RPM
[21:20:39.226] Z轴实际速度: 400 RPM
[21:20:39.226] Z轴最大加速度: 0.4 m/s²
[21:20:39.226] Z轴从站地址: 4
[21:20:39.226] ========================

--- 各轴运动时间估算 ---
全局最大速度限制: 600 RPM

计算motor_x轴，距离=165.19881635904312mm:

--- 运动时间估算详情 ---
导程: 105.0 mm/转
目标速度: 150 RPM = 2.5000 转/秒 = 0.2625 m/s
加速度: 0.6 m/s²
加速时间: 0.4375秒
减速时间: 0.4375秒
加速位移: 0.0574 m
减速位移: 0.0574 m
总位移: 0.1652 m
情况: 达到最高速度，梯形速度曲线
加速时间: 0.4375秒
匀速距离: 0.0504m
匀速时间: 0.1918秒
减速时间: 0.4375秒
总估算时间: 1.0668秒 (加速+匀速+减速)
----------------------
motor_x估算时间: 1.067秒

计算motor_y轴，距离=171.27549198269844mm:

--- 运动时间估算详情 ---
导程: 75.0 mm/转
目标速度: 180 RPM = 3.0000 转/秒 = 0.2250 m/s
加速度: 0.5 m/s²
加速时间: 0.4500秒
减速时间: 0.4500秒
加速位移: 0.0506 m
减速位移: 0.0506 m
总位移: 0.1713 m
情况: 达到最高速度，梯形速度曲线
加速时间: 0.4500秒
匀速距离: 0.0700m
匀速时间: 0.3112秒
减速时间: 0.4500秒
总估算时间: 1.2112秒 (加速+匀速+减速)
----------------------
motor_y估算时间: 1.211秒

计算motor_z轴，距离=-50.0mm:

--- 运动时间估算详情 ---
导程: 21.0 mm/转
目标速度: 400 RPM = 6.6667 转/秒 = 0.1400 m/s
加速度: 0.4 m/s²
加速时间: 0.3500秒
减速时间: 0.3500秒
加速位移: 0.0245 m
减速位移: 0.0245 m
总位移: 0.0500 m
情况: 达到最高速度，梯形速度曲线
加速时间: 0.3500秒
匀速距离: 0.0010m
匀速时间: 0.0071秒
减速时间: 0.3500秒
总估算时间: 0.7071秒 (加速+匀速+减速)
----------------------
motor_z估算时间: 0.707秒
motor_cam: 不移动，时间为0

--- 同步移动时间计算 ---
X轴时间: 1.067秒
Y轴时间: 1.211秒
Z轴时间: 0.707秒
同步移动时间: 1.211秒
motor_x轴: 调整速度为 104.0 RPM 以达到同步时间 1.211秒
motor_y轴: 调整速度为 180.0 RPM 以达到同步时间 1.211秒
Z轴: 应用同步速度补偿系数 1.70。速度从 130.2 RPM 调整为 221.3 RPM。
motor_z轴: 调整速度为 221.3 RPM 以达到同步时间 1.211秒

所有轴时间结果:
 - motor_x: 1.067秒
 - motor_y: 1.211秒
 - motor_z: 0.707秒
 - motor_cam: 0.000秒
Z轴时间: 0.707秒
最长运动时间: 1.211秒
同步移动时间: 1.211秒

DEBUG - 最终调整后的速度值:
motor_x: 103.965 RPM
motor_y: 180.000 RPM
motor_z: 221.321 RPM
motor_cam: 200.000 RPM
------------------------

[21:20:39.226] ===== 运动时间估算 =====
[21:20:39.226] Z轴移动距离: -50.0 mm
[21:20:39.226] 估算Z轴运动时间: 0.707 秒
[21:20:39.226] 所有轴中最长运动时间: 1.211 秒
[21:20:39.226] X、Y、Z轴同步移动时间: 1.211 秒
[21:20:39.226] =======================

[21:20:39.226] Pre-Z delay: 0.04s, then opening DO1 brake.
[21:20:39.295] Sending X command: 165.199 mm, Speed: 104.0 RPM

--- 运动时间估算详情 ---
导程: 105.0 mm/转
目标速度: 104 RPM = 1.7333 转/秒 = 0.1820 m/s
加速度: 0.6 m/s²
加速时间: 0.3033秒
减速时间: 0.3033秒
加速位移: 0.0276 m
减速位移: 0.0276 m
总位移: 0.1652 m
情况: 达到最高速度，梯形速度曲线
加速时间: 0.3033秒
匀速距离: 0.1100m
匀速时间: 0.6044秒
减速时间: 0.3033秒
总估算时间: 1.2110秒 (加速+匀速+减速)
----------------------
[21:20:39.352] Sending Y command: 171.275 mm, Speed: 180.0 RPM
[21:20:39.376] ReqID:unknown ObjID:2 CAMThread - CAM展示: CAM轴移动到展示位置 (250.0mm)。

[21:20:39.376] ===== 准备移动 =====
[21:20:39.376] 请求目标位置: X=不变, Y=不变, Z=不变, CAM=250.0
[21:20:39.376] 调整后物理目标: X=434.8011836409569, Y=170.72450801730156
[21:20:39.376] 当前电机位置: X=364.801, Y=100.725, Z=50.000, CAM=0.000
[21:20:39.376] 使用的轴偏移值 (来自配置): X=70.000, Y=70.000
[21:20:39.376] 计算电机目标: X=364.801, Y=100.725, Z=50.000, CAM=250.000
[21:20:39.376] 计算移动距离: dX=+0.000, dY=+0.000, dZ=+0.000, dCAM=+250.000
[21:20:39.376] DO2状态: 2 (不变(2))
[21:20:39.376] =====================
[21:20:39.376] run_motor_commands invoked.

[21:20:39.376] ===== 全局速度设置 =====
[21:20:39.376] 最大速度限制: 600 RPM
[21:20:39.376] ========================

[21:20:39.376] ===== Z轴配置信息 =====
[21:20:39.376] Z轴导程: 21.0 mm/转
[21:20:39.376] Z轴设置速度: 400 RPM
[21:20:39.376] Z轴实际速度: 400 RPM
[21:20:39.376] Z轴最大加速度: 0.4 m/s²
[21:20:39.376] Z轴从站地址: 4
[21:20:39.376] ========================

--- 各轴运动时间估算 ---
全局最大速度限制: 600 RPM
motor_x: 不移动，时间为0
motor_y: 不移动，时间为0
motor_z: 不移动，时间为0

计算motor_cam轴，距离=250.0mm:

--- 运动时间估算详情 ---
导程: 75.0 mm/转
目标速度: 200 RPM = 3.3333 转/秒 = 0.2500 m/s
加速度: 1 m/s²
加速时间: 0.2500秒
减速时间: 0.2500秒
加速位移: 0.0312 m
减速位移: 0.0312 m
总位移: 0.2500 m
情况: 达到最高速度，梯形速度曲线
加速时间: 0.2500秒
匀速距离: 0.1875m
匀速时间: 0.7500秒
减速时间: 0.2500秒
总估算时间: 1.2500秒 (加速+匀速+减速)
----------------------
motor_cam估算时间: 1.250秒

--- 同步移动时间计算 ---
X轴时间: 0.000秒
Y轴时间: 0.000秒
Z轴时间: 0.000秒
同步移动时间: 0.000秒
CAM轴: 速度为 200 RPM (不同步)

所有轴时间结果:
 - motor_x: 0.000秒
 - motor_y: 0.000秒
 - motor_z: 0.000秒
 - motor_cam: 1.250秒
Z轴时间: 0.000秒
最长运动时间: 1.250秒
同步移动时间: 0.000秒

DEBUG - 最终调整后的速度值:
motor_x: 150.000 RPM
motor_y: 180.000 RPM
motor_z: 400.000 RPM
motor_cam: 200.000 RPM
------------------------

[21:20:39.380] ===== 运动时间估算 =====
[21:20:39.380] Z轴移动距离: 0.0 mm
[21:20:39.380] 估算Z轴运动时间: 0.000 秒
[21:20:39.380] 所有轴中最长运动时间: 1.250 秒
[21:20:39.380] X、Y、Z轴同步移动时间: 0.000 秒
[21:20:39.380] =======================

[21:20:39.382] Sending CAM command: 250.000 mm, Speed: 200.0 RPM

--- 运动时间估算详情 ---
导程: 75.0 mm/转
目标速度: 180 RPM = 3.0000 转/秒 = 0.2250 m/s
加速度: 0.5 m/s²
加速时间: 0.4500秒
减速时间: 0.4500秒
加速位移: 0.0506 m
减速位移: 0.0506 m
总位移: 0.1713 m
情况: 达到最高速度，梯形速度曲线
加速时间: 0.4500秒
匀速距离: 0.0700m
匀速时间: 0.3112秒
减速时间: 0.4500秒
总估算时间: 1.2112秒 (加速+匀速+减速)
----------------------
[21:20:39.427] Sending Z command: -50.000 mm, Speed: 221.3 RPM

--- 运动时间估算详情 ---
导程: 75.0 mm/转
目标速度: 200 RPM = 3.3333 转/秒 = 0.2500 m/s
加速度: 1 m/s²
加速时间: 0.2500秒
减速时间: 0.2500秒
加速位移: 0.0312 m
减速位移: 0.0312 m
总位移: 0.2500 m
情况: 达到最高速度，梯形速度曲线
加速时间: 0.2500秒
匀速距离: 0.1875m
匀速时间: 0.7500秒
减速时间: 0.2500秒
总估算时间: 1.2500秒 (加速+匀速+减速)
----------------------
[21:20:39.466] Starting 1 monitor thread(s) for this command...
[21:20:39.466] motor_cam (Addr: 1): Monitoring started.
[21:20:39.466] All 1 monitor threads for this command have been started.
[21:20:39.467] Now waiting for 1 monitored axis/axes of this command to complete...
[21:20:39.467] Waiting for motor_cam to complete...

--- 运动时间估算详情 ---
导程: 21.0 mm/转
目标速度: 221 RPM = 3.6833 转/秒 = 0.0774 m/s
加速度: 0.4 m/s²
加速时间: 0.1934秒
减速时间: 0.1934秒
加速位移: 0.0075 m
减速位移: 0.0075 m
总位移: 0.0500 m
情况: 达到最高速度，梯形速度曲线
加速时间: 0.1934秒
匀速距离: 0.0350m
匀速时间: 0.4530秒
减速时间: 0.1934秒
总估算时间: 0.8398秒 (加速+匀速+减速)
----------------------
[21:20:39.529] Starting 3 monitor thread(s) for this command...
[21:20:39.530] motor_x (Addr: 3): Monitoring started.
[21:20:39.530] motor_y (Addr: 2): Monitoring started.
[21:20:39.530] Z-Axis Monitor (Addr: 4): Started.
[21:20:39.530] All 3 monitor threads for this command have been started.
[21:20:39.531] Now waiting for 3 monitored axis/axes of this command to complete...
[21:20:39.531] Waiting for motor_x to complete...
[21:20:39.548] motor_cam: Initial status is RUNNING.
[21:20:39.599] motor_x: Initial status is RUNNING.
[21:20:39.651] motor_y: Initial status is RUNNING.
[21:20:40.515] Z-axis (Addr: 4) status: 电机停止 (Status: 0x0002). Activating brake.
[21:20:40.638] Z-Axis Monitor (Addr: 4): Ended (normal or error).
[21:20:40.793] motor_x completed.
[21:20:40.793] Waiting for motor_y to complete...
[21:20:40.845] motor_y completed.
[21:20:40.845] Waiting for motor_z to complete...
[21:20:40.863] motor_z completed.
[21:20:40.863] All monitored axes for this command instance completed.
[21:20:40.863] Post-Z delay: 0.0s, then closing DO1 brake.
[21:20:40.863] Ensuring DO1 brake is closed.
[21:20:40.914] run_motor_commands finished.
[21:20:40.914] 移动操作成功完成! 新电机位置: X=530.000 (已更新), Y=272.000 (已更新), Z=0.000 (已更新), CAM=0.000
ReqID:unknown 物体编号:2 - 步骤 11: XYZ流程: 展示位置停留 1.0秒 (抓取成功后)
[21:20:40.932] motor_cam completed.
[21:20:40.937] All monitored axes for this command instance completed.
[21:20:40.937] run_motor_commands finished.
[21:20:40.937] 移动操作成功完成! 新电机位置: X=530.000, Y=272.000, Z=0.000, CAM=250.000 (已更新)
[21:20:40.937] ReqID:unknown ObjID:2 CAMThread - CAM展示: CAM轴在展示位停留 0.3 秒。
[21:20:41.237] ReqID:unknown ObjID:2 CAMThread - CAM展示: CAM轴回到初始位置 (0.0mm)。

[21:20:41.237] ===== 准备移动 =====
[21:20:41.237] 请求目标位置: X=不变, Y=不变, Z=不变, CAM=0.0
[21:20:41.237] 调整后物理目标: X=600.0, Y=342.0
[21:20:41.237] 当前电机位置: X=530.000, Y=272.000, Z=0.000, CAM=250.000
[21:20:41.237] 使用的轴偏移值 (来自配置): X=70.000, Y=70.000
[21:20:41.237] 计算电机目标: X=530.000, Y=272.000, Z=0.000, CAM=0.000
[21:20:41.237] 计算移动距离: dX=+0.000, dY=+0.000, dZ=+0.000, dCAM=-250.000
[21:20:41.237] DO2状态: 2 (不变(2))
[21:20:41.237] =====================
[21:20:41.239] run_motor_commands invoked.

[21:20:41.239] ===== 全局速度设置 =====
[21:20:41.239] 最大速度限制: 600 RPM
[21:20:41.239] ========================

[21:20:41.239] ===== Z轴配置信息 =====
[21:20:41.239] Z轴导程: 21.0 mm/转
[21:20:41.239] Z轴设置速度: 400 RPM
[21:20:41.239] Z轴实际速度: 400 RPM
[21:20:41.239] Z轴最大加速度: 0.4 m/s²
[21:20:41.239] Z轴从站地址: 4
[21:20:41.239] ========================

--- 各轴运动时间估算 ---
全局最大速度限制: 600 RPM
motor_x: 不移动，时间为0
motor_y: 不移动，时间为0
motor_z: 不移动，时间为0

计算motor_cam轴，距离=-250.0mm:

--- 运动时间估算详情 ---
导程: 75.0 mm/转
目标速度: 200 RPM = 3.3333 转/秒 = 0.2500 m/s
加速度: 1 m/s²
加速时间: 0.2500秒
减速时间: 0.2500秒
加速位移: 0.0312 m
减速位移: 0.0312 m
总位移: 0.2500 m
情况: 达到最高速度，梯形速度曲线
加速时间: 0.2500秒
匀速距离: 0.1875m
匀速时间: 0.7500秒
减速时间: 0.2500秒
总估算时间: 1.2500秒 (加速+匀速+减速)
----------------------
motor_cam估算时间: 1.250秒

--- 同步移动时间计算 ---
X轴时间: 0.000秒
Y轴时间: 0.000秒
Z轴时间: 0.000秒
同步移动时间: 0.000秒
CAM轴: 速度为 200 RPM (不同步)

所有轴时间结果:
 - motor_x: 0.000秒
 - motor_y: 0.000秒
 - motor_z: 0.000秒
 - motor_cam: 1.250秒
Z轴时间: 0.000秒
最长运动时间: 1.250秒
同步移动时间: 0.000秒

DEBUG - 最终调整后的速度值:
motor_x: 150.000 RPM
motor_y: 180.000 RPM
motor_z: 400.000 RPM
motor_cam: 200.000 RPM
------------------------

[21:20:41.240] ===== 运动时间估算 =====
[21:20:41.240] Z轴移动距离: 0.0 mm
[21:20:41.240] 估算Z轴运动时间: 0.000 秒
[21:20:41.240] 所有轴中最长运动时间: 1.250 秒
[21:20:41.240] X、Y、Z轴同步移动时间: 0.000 秒
[21:20:41.240] =======================

[21:20:41.240] Sending CAM command: -250.000 mm, Speed: 200.0 RPM

--- 运动时间估算详情 ---
导程: 75.0 mm/转
目标速度: 200 RPM = 3.3333 转/秒 = 0.2500 m/s
加速度: 1 m/s²
加速时间: 0.2500秒
减速时间: 0.2500秒
加速位移: 0.0312 m
减速位移: 0.0312 m
总位移: 0.2500 m
情况: 达到最高速度，梯形速度曲线
加速时间: 0.2500秒
匀速距离: 0.1875m
匀速时间: 0.7500秒
减速时间: 0.2500秒
总估算时间: 1.2500秒 (加速+匀速+减速)
----------------------
[21:20:41.318] Starting 1 monitor thread(s) for this command...
[21:20:41.318] motor_cam (Addr: 1): Monitoring started.
[21:20:41.318] All 1 monitor threads for this command have been started.
[21:20:41.318] Now waiting for 1 monitored axis/axes of this command to complete...
[21:20:41.318] Waiting for motor_cam to complete...
[21:20:41.370] motor_cam: Initial status is RUNNING.
ReqID:unknown 物体编号:2 - 步骤 12: XYZ流程: 获取当前物体位置，计算最佳投放点

===== 计算最佳投放位置 =====
鱼缸尺寸: 685.0 x 685.0 mm
安全边距: 200.0 mm
粗网格大小: 100.0 mm
细网格大小: 10.0 mm
检测到的物体数量: 2
开始粗略搜索...
粗略搜索结果: (400.0, 200.0), 最小距离: 228.6 mm
开始精细搜索...
精细搜索结果: (480.0, 200.0), 最小距离: 282.4 mm
===== 最佳投放位置计算完成 =====
ReqID:unknown 物体编号:2 - 步骤 13: XYZ流程: 移动到投放位置 (480.0, 200.0, 200.0)

[21:20:41.922] ===== 准备移动 =====
[21:20:41.922] 请求目标位置: X=480.0, Y=200.0, Z=200.0, CAM=不变
[21:20:41.922] 调整后物理目标: X=480.0, Y=200.0
[21:20:41.922] 当前电机位置: X=530.000, Y=272.000, Z=0.000, CAM=250.000
[21:20:41.922] 使用的轴偏移值 (来自配置): X=70.000, Y=70.000
[21:20:41.922] 计算电机目标: X=410.000, Y=130.000, Z=200.000, CAM=250.000
[21:20:41.922] 计算移动距离: dX=-120.000, dY=-142.000, dZ=+200.000, dCAM=+0.000
[21:20:41.922] DO2状态: 2 (不变(2))
[21:20:41.922] =====================
[21:20:41.922] run_motor_commands invoked.

[21:20:41.922] ===== 全局速度设置 =====
[21:20:41.922] 最大速度限制: 600 RPM
[21:20:41.922] ========================

[21:20:41.922] ===== Z轴配置信息 =====
[21:20:41.922] Z轴导程: 21.0 mm/转
[21:20:41.922] Z轴设置速度: 400 RPM
[21:20:41.922] Z轴实际速度: 400 RPM
[21:20:41.922] Z轴最大加速度: 0.4 m/s²
[21:20:41.922] Z轴从站地址: 4
[21:20:41.922] ========================

--- 各轴运动时间估算 ---
全局最大速度限制: 600 RPM

计算motor_x轴，距离=-120.0mm:

--- 运动时间估算详情 ---
导程: 105.0 mm/转
目标速度: 150 RPM = 2.5000 转/秒 = 0.2625 m/s
加速度: 0.6 m/s²
加速时间: 0.4375秒
减速时间: 0.4375秒
加速位移: 0.0574 m
减速位移: 0.0574 m
总位移: 0.1200 m
情况: 达到最高速度，梯形速度曲线
加速时间: 0.4375秒
匀速距离: 0.0052m
匀速时间: 0.0196秒
减速时间: 0.4375秒
总估算时间: 0.8946秒 (加速+匀速+减速)
----------------------
motor_x估算时间: 0.895秒

计算motor_y轴，距离=-142.0mm:

--- 运动时间估算详情 ---
导程: 75.0 mm/转
目标速度: 180 RPM = 3.0000 转/秒 = 0.2250 m/s
加速度: 0.5 m/s²
加速时间: 0.4500秒
减速时间: 0.4500秒
加速位移: 0.0506 m
减速位移: 0.0506 m
总位移: 0.1420 m
情况: 达到最高速度，梯形速度曲线
加速时间: 0.4500秒
匀速距离: 0.0408m
匀速时间: 0.1811秒
减速时间: 0.4500秒
总估算时间: 1.0811秒 (加速+匀速+减速)
----------------------
motor_y估算时间: 1.081秒

计算motor_z轴，距离=200.0mm:

--- 运动时间估算详情 ---
导程: 21.0 mm/转
目标速度: 400 RPM = 6.6667 转/秒 = 0.1400 m/s
加速度: 0.4 m/s²
加速时间: 0.3500秒
减速时间: 0.3500秒
加速位移: 0.0245 m
减速位移: 0.0245 m
总位移: 0.2000 m
情况: 达到最高速度，梯形速度曲线
加速时间: 0.3500秒
匀速距离: 0.1510m
匀速时间: 1.0786秒
减速时间: 0.3500秒
总估算时间: 1.7786秒 (加速+匀速+减速)
----------------------
motor_z估算时间: 1.779秒
motor_cam: 不移动，时间为0

--- 同步移动时间计算 ---
X轴时间: 0.895秒
Y轴时间: 1.081秒
Z轴时间: 1.779秒
同步移动时间: 1.779秒
motor_x轴: 调整速度为 41.4 RPM 以达到同步时间 1.779秒
motor_y轴: 调整速度为 70.9 RPM 以达到同步时间 1.779秒
Z轴: 应用同步速度补偿系数 1.70。速度从 400.0 RPM 调整为 680.0 RPM。
Z轴: 补偿后速度 680.0 RPM 超过有效最大限制 400.0 RPM，将使用限制值。
motor_z轴: 调整速度为 400.0 RPM 以达到同步时间 1.779秒

所有轴时间结果:
 - motor_x: 0.895秒
 - motor_y: 1.081秒
 - motor_z: 1.779秒
 - motor_cam: 0.000秒
Z轴时间: 1.779秒
最长运动时间: 1.779秒
同步移动时间: 1.779秒

DEBUG - 最终调整后的速度值:
motor_x: 41.359 RPM
motor_y: 70.947 RPM
motor_z: 400.000 RPM
motor_cam: 200.000 RPM
------------------------

[21:20:41.928] ===== 运动时间估算 =====
[21:20:41.928] Z轴移动距离: 200.0 mm
[21:20:41.928] 估算Z轴运动时间: 1.779 秒
[21:20:41.928] 所有轴中最长运动时间: 1.779 秒
[21:20:41.928] X、Y、Z轴同步移动时间: 1.779 秒
[21:20:41.928] =======================

[21:20:41.929] Pre-Z delay: 0.04s, then opening DO1 brake.
[21:20:42.039] Sending X command: -120.000 mm, Speed: 41.4 RPM

--- 运动时间估算详情 ---
导程: 105.0 mm/转
目标速度: 41 RPM = 0.6833 转/秒 = 0.0717 m/s
加速度: 0.6 m/s²
加速时间: 0.1196秒
减速时间: 0.1196秒
加速位移: 0.0043 m
减速位移: 0.0043 m
总位移: 0.1200 m
情况: 达到最高速度，梯形速度曲线
加速时间: 0.1196秒
匀速距离: 0.1114m
匀速时间: 1.5529秒
减速时间: 0.1196秒
总估算时间: 1.7921秒 (加速+匀速+减速)
----------------------
[21:20:42.108] Sending Y command: -142.000 mm, Speed: 70.9 RPM

--- 运动时间估算详情 ---
导程: 75.0 mm/转
目标速度: 71 RPM = 1.1833 转/秒 = 0.0887 m/s
加速度: 0.5 m/s²
加速时间: 0.1775秒
减速时间: 0.1775秒
加速位移: 0.0079 m
减速位移: 0.0079 m
总位移: 0.1420 m
情况: 达到最高速度，梯形速度曲线
加速时间: 0.1775秒
匀速距离: 0.1262m
匀速时间: 1.4225秒
减速时间: 0.1775秒
总估算时间: 1.7775秒 (加速+匀速+减速)
----------------------
[21:20:42.202] Sending Z command: 200.000 mm, Speed: 400.0 RPM

--- 运动时间估算详情 ---
导程: 21.0 mm/转
目标速度: 400 RPM = 6.6667 转/秒 = 0.1400 m/s
加速度: 0.4 m/s²
加速时间: 0.3500秒
减速时间: 0.3500秒
加速位移: 0.0245 m
减速位移: 0.0245 m
总位移: 0.2000 m
情况: 达到最高速度，梯形速度曲线
加速时间: 0.3500秒
匀速距离: 0.1510m
匀速时间: 1.0786秒
减速时间: 0.3500秒
总估算时间: 1.7786秒 (加速+匀速+减速)
----------------------
[21:20:42.317] Starting 3 monitor thread(s) for this command...
[21:20:42.317] motor_x (Addr: 3): Monitoring started.
[21:20:42.317] motor_y (Addr: 2): Monitoring started.
[21:20:42.317] Z-Axis Monitor (Addr: 4): Started.
[21:20:42.317] All 3 monitor threads for this command have been started.
[21:20:42.317] Now waiting for 3 monitored axis/axes of this command to complete...
[21:20:42.317] Waiting for motor_x to complete...
[21:20:42.419] motor_x: Initial status is RUNNING.
[21:20:42.470] motor_y: Initial status is RUNNING.
[21:20:42.783] motor_cam completed.
[21:20:42.783] All monitored axes for this command instance completed.
[21:20:42.783] run_motor_commands finished.
[21:20:42.783] 移动操作成功完成! 新电机位置: X=530.000, Y=272.000, Z=0.000, CAM=0.000 (已更新)
[21:20:42.783] ReqID:unknown ObjID:2 CAMThread - CAM轴展示序列完成。线程结束。
[21:20:42.783] ReqID:unknown ObjID:2 CAMThread - CAM展示线程最终退出。
[21:20:44.087] motor_x completed.
[21:20:44.087] Waiting for motor_y to complete...
[21:20:44.139] motor_y completed.
[21:20:44.139] Waiting for motor_z to complete...
[21:20:44.265] Z-axis (Addr: 4) status: 电机停止 (Status: 0x0002). Activating brake.
[21:20:44.287] Z-Axis Monitor (Addr: 4): Ended (normal or error).
[21:20:44.316] motor_z completed.
[21:20:44.316] All monitored axes for this command instance completed.
[21:20:44.316] Post-Z delay: 0.0s, then closing DO1 brake.
[21:20:44.316] Ensuring DO1 brake is closed.
[21:20:44.340] run_motor_commands finished.
[21:20:44.355] 移动操作成功完成! 新电机位置: X=410.000 (已更新), Y=130.000 (已更新), Z=200.000 (已更新), CAM=0.000
ReqID:unknown 物体编号:2 - 步骤 14: XYZ流程: 打开爪子释放物体(DO2=1)

[21:20:44.355] ===== 准备移动 =====
[21:20:44.355] 请求目标位置: X=不变, Y=不变, Z=不变, CAM=不变
[21:20:44.355] 调整后物理目标: X=480.0, Y=200.0
[21:20:44.355] 当前电机位置: X=410.000, Y=130.000, Z=200.000, CAM=0.000
[21:20:44.355] 使用的轴偏移值 (来自配置): X=70.000, Y=70.000
[21:20:44.355] 计算电机目标: X=410.000, Y=130.000, Z=200.000, CAM=0.000
[21:20:44.355] 计算移动距离: dX=+0.000, dY=+0.000, dZ=+0.000, dCAM=+0.000
[21:20:44.355] DO2状态: 1 (释放(1))
[21:20:44.355] =====================
[21:20:44.357] run_motor_commands invoked.

[21:20:44.357] ===== 全局速度设置 =====
[21:20:44.357] 最大速度限制: 600 RPM
[21:20:44.357] ========================

[21:20:44.357] ===== Z轴配置信息 =====
[21:20:44.357] Z轴导程: 21.0 mm/转
[21:20:44.357] Z轴设置速度: 400 RPM
[21:20:44.357] Z轴实际速度: 400 RPM
[21:20:44.357] Z轴最大加速度: 0.4 m/s²
[21:20:44.357] Z轴从站地址: 4
[21:20:44.357] ========================

--- 各轴运动时间估算 ---
全局最大速度限制: 600 RPM
motor_x: 不移动，时间为0
motor_y: 不移动，时间为0
motor_z: 不移动，时间为0
motor_cam: 不移动，时间为0

--- 同步移动时间计算 ---
X轴时间: 0.000秒
Y轴时间: 0.000秒
Z轴时间: 0.000秒
同步移动时间: 0.000秒

所有轴时间结果:
 - motor_x: 0.000秒
 - motor_y: 0.000秒
 - motor_z: 0.000秒
 - motor_cam: 0.000秒
Z轴时间: 0.000秒
最长运动时间: 0.000秒
同步移动时间: 0.000秒

DEBUG - 最终调整后的速度值:
motor_x: 150.000 RPM
motor_y: 180.000 RPM
motor_z: 400.000 RPM
motor_cam: 200.000 RPM
------------------------

[21:20:44.362] ===== 运动时间估算 =====
[21:20:44.362] Z轴移动距离: 0.0 mm
[21:20:44.362] 估算Z轴运动时间: 0.000 秒
[21:20:44.362] 所有轴中最长运动时间: 0.000 秒
[21:20:44.362] X、Y、Z轴同步移动时间: 0.000 秒
[21:20:44.362] =======================

[21:20:44.363] Controlling DO2 to: ON
[21:20:44.385] No monitor threads to start for this command.
[21:20:44.385] No axes being monitored for completion in this command.
[21:20:44.389] All monitored axes for this command instance completed.
[21:20:44.389] run_motor_commands finished.
[21:20:44.389] 移动操作成功完成! 新电机位置: X=410.000, Y=130.000, Z=200.000, CAM=0.000
ReqID:unknown 物体编号:2 - 步骤 15: XYZ流程: 等待物体释放稳定时间 0.5秒
ReqID:unknown 物体编号:2 - 步骤 16: XYZ流程: 回到初始位置 (抓取成功后)

[21:20:44.890] ===== 准备移动 =====
[21:20:44.890] 请求目标位置: X=70.0, Y=70.0, Z=0.0, CAM=不变
[21:20:44.890] 调整后物理目标: X=70.0, Y=70.0
[21:20:44.890] 当前电机位置: X=410.000, Y=130.000, Z=200.000, CAM=0.000
[21:20:44.890] 使用的轴偏移值 (来自配置): X=70.000, Y=70.000
[21:20:44.890] 计算电机目标: X=0.000, Y=0.000, Z=0.000, CAM=0.000
[21:20:44.890] 计算移动距离: dX=-410.000, dY=-130.000, dZ=-200.000, dCAM=+0.000
[21:20:44.890] DO2状态: 2 (不变(2))
[21:20:44.890] =====================
[21:20:44.893] run_motor_commands invoked.

[21:20:44.893] ===== 全局速度设置 =====
[21:20:44.893] 最大速度限制: 600 RPM
[21:20:44.893] ========================

[21:20:44.893] ===== Z轴配置信息 =====
[21:20:44.893] Z轴导程: 21.0 mm/转
[21:20:44.893] Z轴设置速度: 400 RPM
[21:20:44.893] Z轴实际速度: 400 RPM
[21:20:44.893] Z轴最大加速度: 0.4 m/s²
[21:20:44.893] Z轴从站地址: 4
[21:20:44.893] ========================

--- 各轴运动时间估算 ---
全局最大速度限制: 600 RPM

计算motor_x轴，距离=-410.0mm:

--- 运动时间估算详情 ---
导程: 105.0 mm/转
目标速度: 150 RPM = 2.5000 转/秒 = 0.2625 m/s
加速度: 0.6 m/s²
加速时间: 0.4375秒
减速时间: 0.4375秒
加速位移: 0.0574 m
减速位移: 0.0574 m
总位移: 0.4100 m
情况: 达到最高速度，梯形速度曲线
加速时间: 0.4375秒
匀速距离: 0.2952m
匀速时间: 1.1244秒
减速时间: 0.4375秒
总估算时间: 1.9994秒 (加速+匀速+减速)
----------------------
motor_x估算时间: 1.999秒

计算motor_y轴，距离=-130.0mm:

--- 运动时间估算详情 ---
导程: 75.0 mm/转
目标速度: 180 RPM = 3.0000 转/秒 = 0.2250 m/s
加速度: 0.5 m/s²
加速时间: 0.4500秒
减速时间: 0.4500秒
加速位移: 0.0506 m
减速位移: 0.0506 m
总位移: 0.1300 m
情况: 达到最高速度，梯形速度曲线
加速时间: 0.4500秒
匀速距离: 0.0288m
匀速时间: 0.1278秒
减速时间: 0.4500秒
总估算时间: 1.0278秒 (加速+匀速+减速)
----------------------
motor_y估算时间: 1.028秒

计算motor_z轴，距离=-200.0mm:

--- 运动时间估算详情 ---
导程: 21.0 mm/转
目标速度: 400 RPM = 6.6667 转/秒 = 0.1400 m/s
加速度: 0.4 m/s²
加速时间: 0.3500秒
减速时间: 0.3500秒
加速位移: 0.0245 m
减速位移: 0.0245 m
总位移: 0.2000 m
情况: 达到最高速度，梯形速度曲线
加速时间: 0.3500秒
匀速距离: 0.1510m
匀速时间: 1.0786秒
减速时间: 0.3500秒
总估算时间: 1.7786秒 (加速+匀速+减速)
----------------------
motor_z估算时间: 1.779秒
motor_cam: 不移动，时间为0

--- 同步移动时间计算 ---
X轴时间: 1.999秒
Y轴时间: 1.028秒
Z轴时间: 1.779秒
同步移动时间: 1.999秒
motor_x轴: 计算同步速度 150.0 RPM 超过有效最大限制 150.0 RPM，将使用限制值。
motor_x轴: 调整速度为 150.0 RPM 以达到同步时间 1.999秒
motor_y轴: 调整速度为 55.9 RPM 以达到同步时间 1.999秒
Z轴: 应用同步速度补偿系数 1.70。速度从 334.9 RPM 调整为 569.3 RPM。
Z轴: 补偿后速度 569.3 RPM 超过有效最大限制 400.0 RPM，将使用限制值。
motor_z轴: 调整速度为 400.0 RPM 以达到同步时间 1.999秒

所有轴时间结果:
 - motor_x: 1.999秒
 - motor_y: 1.028秒
 - motor_z: 1.779秒
 - motor_cam: 0.000秒
Z轴时间: 1.779秒
最长运动时间: 1.999秒
同步移动时间: 1.999秒

DEBUG - 最终调整后的速度值:
motor_x: 150.000 RPM
motor_y: 55.926 RPM
motor_z: 400.000 RPM
motor_cam: 200.000 RPM
------------------------

[21:20:44.898] ===== 运动时间估算 =====
[21:20:44.898] Z轴移动距离: -200.0 mm
[21:20:44.898] 估算Z轴运动时间: 1.779 秒
[21:20:44.898] 所有轴中最长运动时间: 1.999 秒
[21:20:44.898] X、Y、Z轴同步移动时间: 1.999 秒
[21:20:44.898] =======================

[21:20:44.898] Pre-Z delay: 0.04s, then opening DO1 brake.
[21:20:44.975] Sending X command: -410.000 mm, Speed: 150.0 RPM

--- 运动时间估算详情 ---
导程: 105.0 mm/转
目标速度: 150 RPM = 2.5000 转/秒 = 0.2625 m/s
加速度: 0.6 m/s²
加速时间: 0.4375秒
减速时间: 0.4375秒
加速位移: 0.0574 m
减速位移: 0.0574 m
总位移: 0.4100 m
情况: 达到最高速度，梯形速度曲线
加速时间: 0.4375秒
匀速距离: 0.2952m
匀速时间: 1.1244秒
减速时间: 0.4375秒
总估算时间: 1.9994秒 (加速+匀速+减速)
----------------------
[21:20:45.034] Sending Y command: -130.000 mm, Speed: 55.9 RPM

--- 运动时间估算详情 ---
导程: 75.0 mm/转
目标速度: 56 RPM = 0.9333 转/秒 = 0.0700 m/s
加速度: 0.5 m/s²
加速时间: 0.1400秒
减速时间: 0.1400秒
加速位移: 0.0049 m
减速位移: 0.0049 m
总位移: 0.1300 m
情况: 达到最高速度，梯形速度曲线
加速时间: 0.1400秒
匀速距离: 0.1202m
匀速时间: 1.7171秒
减速时间: 0.1400秒
总估算时间: 1.9971秒 (加速+匀速+减速)
----------------------
[21:20:45.096] Sending Z command: -200.000 mm, Speed: 400.0 RPM

--- 运动时间估算详情 ---
导程: 21.0 mm/转
目标速度: 400 RPM = 6.6667 转/秒 = 0.1400 m/s
加速度: 0.4 m/s²
加速时间: 0.3500秒
减速时间: 0.3500秒
加速位移: 0.0245 m
减速位移: 0.0245 m
总位移: 0.2000 m
情况: 达到最高速度，梯形速度曲线
加速时间: 0.3500秒
匀速距离: 0.1510m
匀速时间: 1.0786秒
减速时间: 0.3500秒
总估算时间: 1.7786秒 (加速+匀速+减速)
----------------------
[21:20:45.144] Starting 3 monitor thread(s) for this command...
[21:20:45.144] motor_x (Addr: 3): Monitoring started.
[21:20:45.144] motor_y (Addr: 2): Monitoring started.
[21:20:45.148] Z-Axis Monitor (Addr: 4): Started.
[21:20:45.148] All 3 monitor threads for this command have been started.
[21:20:45.149] Now waiting for 3 monitored axis/axes of this command to complete...
[21:20:45.149] Waiting for motor_x to complete...
[21:20:45.199] motor_x: Initial status is RUNNING.
[21:20:45.250] motor_y: Initial status is RUNNING.
[21:20:47.135] Z-axis (Addr: 4) status: 电机停止 (Status: 0x0002). Activating brake.
[21:20:47.218] motor_x completed.
[21:20:47.218] Waiting for motor_y to complete...
[21:20:47.258] Z-Axis Monitor (Addr: 4): Ended (normal or error).
[21:20:47.285] motor_y completed.
[21:20:47.285] Waiting for motor_z to complete...
[21:20:47.285] motor_z completed.
[21:20:47.285] All monitored axes for this command instance completed.
[21:20:47.285] Post-Z delay: 0.0s, then closing DO1 brake.
[21:20:47.285] Ensuring DO1 brake is closed.
[21:20:47.307] run_motor_commands finished.
[21:20:47.314] 移动操作成功完成! 新电机位置: X=0.000 (已更新), Y=0.000 (已更新), Z=0.000 (已更新), CAM=0.000
ReqID:unknown 物体编号:2 - 成功抓取并投放物体，主流程操作结束。CAM展示线程（如果启动）将独立运行。
ReqID:unknown 物体编号:2 - ===== 抓取主流程完成 =====
[21:20:47.314] ReqID:unknown 物体编号:2 - 抓取周期函数 move_to_object 执行完毕，将触发配置重载。

[21:20:47.314] ===== 游戏周期结束，检查配置更新 =====
[21:20:47.315] 配置文件 Config_Move.yaml 未修改，跳过更新
[21:20:47.315] 配置文件未修改，无需更新
[21:20:47.315] ===== 配置检查流程结束 =====


当前检测到 3 个物体:
--------------------------------------------------------------------------------------
编号  |  类别ID      |  物理坐标(mm)   | 归一化比例 | 置信度 | 估算时间(秒)
--------------------------------------------------------------------------------------
1     | 0  (螃蟹    ) | (172.3, 217.5)  | (0.251, 0.317)     | 0.93   | N/A
2     | 2  (贝壳    ) | (476.3, 313.6)  | (0.695, 0.458)     | 0.93   | N/A
3     | 1  (小龙虾   ) | (396.8, 470.5)  | (0.579, 0.687)     | 0.91   | N/A
--------------------------------------------------------------------------------------
输入物体编号移动到该位置，输入'r'刷新物体列表，输入'q'退出

请输入物体编号或命令(r=刷新, q=退出): q
用户请求退出程序

程序正在退出，开始清理资源...
正在停止检测客户端...
[21:20:51.013] Socket错误: [WinError 10053] 你的主机中的软件中止了一个已建立的连接。
[21:20:51.013] 检测结果接收线程结束
正在停止游戏服务端...
正在发送退出信号给指令处理线程...
等待指令处理线程退出...
指令处理线程结束
等待游戏服务线程退出...
[21:20:51.083] 游戏服务器已关闭
关闭串口连接

程序已结束