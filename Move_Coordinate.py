"""
坐标转换模块，将归一化比例坐标转换为物理坐标（毫米）
不再依赖Config_Detect_Cam.yaml，直接处理检测模块提供的归一化坐标
可独立运行；交互式输入归一化比例坐标
运行后：请输入归一化比例坐标 (norm_x norm_y): 0.5 0.3
输出：
输入的归一化比例坐标: (0.5, 0.3)
转换后的物理坐标: (150.0mm, 90.0mm)
"""

import yaml

def load_motor_config(config_path='Config_Move.yaml'):
    """加载电机配置信息"""
    try:
        with open(config_path, 'r', encoding='utf-8') as file:
            config = yaml.safe_load(file)
        return config
    except Exception as e:
        print(f"加载电机配置文件失败: {str(e)}")
        return None

def get_tank_dimensions(motor_config_input='Config_Move.yaml'):
    """
    从电机配置中获取鱼缸的物理尺寸

    Args:
        motor_config_input: 配置文件路径(str) 或已加载的配置字典(dict)

    Returns:
        tuple: (tank_x, tank_y) 鱼缸尺寸，失败时返回 None
    """
    motor_config = None

    if isinstance(motor_config_input, str):
        # 输入是文件路径，需要加载
        motor_config = load_motor_config(motor_config_input)
    elif isinstance(motor_config_input, dict):
        # 输入已经是配置字典
        motor_config = motor_config_input
    else:
        print(f"错误: get_tank_dimensions 收到无效的配置输入类型: {type(motor_config_input)}")
        return None

    if not motor_config or 'fish_tank' not in motor_config:
        print("警告: 配置中未找到鱼缸尺寸配置(fish_tank)")
        return None

    tank_x = motor_config['fish_tank'].get('tank_x')
    tank_y = motor_config['fish_tank'].get('tank_y')

    if tank_x is None or tank_y is None:
        print("警告: 配置中鱼缸尺寸(tank_x 或 tank_y)缺失")
        return None

    return float(tank_x), float(tank_y)

def normalized_to_physical_coordinates(normalized_x, normalized_y, tank_dimensions):
    """
    将归一化比例坐标 (0-1范围) 转换为物理坐标 (毫米)

    Args:
        normalized_x (float): X轴的归一化坐标 (0 到 1)
        normalized_y (float): Y轴的归一化坐标 (0 到 1)
        tank_dimensions (tuple): 包含鱼缸物理尺寸 (tank_x_mm, tank_y_mm) 的元组

    Returns:
        tuple: (physical_x, physical_y) 物理坐标 (毫米)
               如果输入无效或tank_dimensions无效，则返回 (None, None)
    """
    if normalized_x is None or normalized_y is None:
        print("错误: 归一化坐标包含None值")
        return None, None
    if tank_dimensions is None or len(tank_dimensions) != 2:
        print("错误: 鱼缸尺寸无效")
        return None, None

    tank_x_mm, tank_y_mm = tank_dimensions

    # 将归一化坐标转换为物理坐标
    physical_x = normalized_x * tank_x_mm
    physical_y = normalized_y * tank_y_mm
    
    return physical_x, physical_y

def clamp_physical_coordinates_to_safe_range(target_x, target_y, tank_dimensions, axis_offsets):
    """
    将物理坐标限制在安全范围内，确保机械爪不会撞击鱼缸边缘
    
    Args:
        target_x (float): 目标X物理坐标（毫米），可以是None
        target_y (float): 目标Y物理坐标（毫米），可以是None
        tank_dimensions (tuple): 鱼缸尺寸(tank_x_mm, tank_y_mm)
        axis_offsets (dict): 轴偏移字典，包含'X'和'Y'键的偏移值
        
    Returns:
        tuple: (clamped_x, clamped_y) 限制后的物理坐标（毫米）
               如果输入为None，对应返回值也为None
    """
    clamped_x, clamped_y = target_x, target_y
    
    if tank_dimensions is None or len(tank_dimensions) != 2:
        print("警告: 鱼缸尺寸无效，无法进行安全范围限制")
        return target_x, target_y
        
    tank_x_mm, tank_y_mm = tank_dimensions
    min_safe_x = axis_offsets.get('X', 0.0)
    min_safe_y = axis_offsets.get('Y', 0.0)
    max_safe_x = tank_x_mm - min_safe_x
    max_safe_y = tank_y_mm - min_safe_y
    
    # 只在值非None时进行限制
    if target_x is not None:
        clamped_x = max(min_safe_x, min(target_x, max_safe_x))
        if clamped_x != target_x:
            print(f"警告: X轴目标坐标 {target_x:.2f}mm 超出安全范围 [{min_safe_x:.2f}, {max_safe_x:.2f}]mm，已调整为 {clamped_x:.2f}mm")
            
    if target_y is not None:
        clamped_y = max(min_safe_y, min(target_y, max_safe_y))
        if clamped_y != target_y:
            print(f"警告: Y轴目标坐标 {target_y:.2f}mm 超出安全范围 [{min_safe_y:.2f}, {max_safe_y:.2f}]mm，已调整为 {clamped_y:.2f}mm")
            
    return clamped_x, clamped_y

def validate_and_adjust_coordinates(target_x, target_y, target_z, target_cam, 
                                   current_positions, axis_offsets, config, tank_dimensions):
    """
    验证并调整坐标，确保在安全范围内
    
    Args:
        target_x, target_y, target_z, target_cam: 目标坐标
        current_positions: 当前位置字典
        axis_offsets: 轴偏移字典
        config: 配置对象
        tank_dimensions: 鱼缸尺寸
        
    Returns:
        tuple: (adjusted_x, adjusted_y, adjusted_z, adjusted_cam, is_valid, error_message)
    """
    # 使用当前位置作为默认值
    adjusted_x = target_x if target_x is not None else current_positions['X'] + axis_offsets.get('X', 0.0)
    adjusted_y = target_y if target_y is not None else current_positions['Y'] + axis_offsets.get('Y', 0.0)
    adjusted_z = target_z if target_z is not None else current_positions['Z']
    adjusted_cam = target_cam if target_cam is not None else current_positions['CAM']
    
    # 应用安全限制
    clamped_x, clamped_y = clamp_physical_coordinates_to_safe_range(
        adjusted_x, adjusted_y, tank_dimensions, axis_offsets
    )
    
    # 验证Z和CAM轴
    is_valid = True
    error_message = ""
    
    # 验证Z轴
    if adjusted_z < 0:
        is_valid = False
        error_message = "Z轴位置不能为负数"
    elif 'motor_z' in config and adjusted_z > config['motor_z'].get('max_distance', float('inf')):
        is_valid = False
        error_message = f"Z轴位置超出最大限制 {config['motor_z'].get('max_distance')}mm"
    
    # 验证CAM轴
    if adjusted_cam < 0:
        is_valid = False
        error_message = "CAM轴位置不能为负数"
    elif 'motor_cam' in config and adjusted_cam > config['motor_cam'].get('max_distance', float('inf')):
        is_valid = False
        error_message = f"CAM轴位置超出最大限制 {config['motor_cam'].get('max_distance')}mm"
    
    return clamped_x, clamped_y, adjusted_z, adjusted_cam, is_valid, error_message

def calculate_motor_deltas(target_x, target_y, target_z, target_cam, 
                          current_positions, axis_offsets):
    """
    计算电机需要移动的距离
    
    Args:
        target_x, target_y, target_z, target_cam: 目标物理坐标
        current_positions: 当前电机位置
        axis_offsets: 轴偏移
        
    Returns:
        dict: 包含各轴移动距离的字典
    """
    # 计算实际电机目标位置（考虑偏移）
    actual_motor_target_x = max(0.0, target_x - axis_offsets.get('X', 0.0))
    actual_motor_target_y = max(0.0, target_y - axis_offsets.get('Y', 0.0))
    
    # 计算各轴移动距离
    delta_x = actual_motor_target_x - current_positions['X']
    delta_y = actual_motor_target_y - current_positions['Y']
    delta_z = target_z - current_positions['Z']
    delta_cam = target_cam - current_positions['CAM']
    
    return {
        'motor_x': delta_x,
        'motor_y': delta_y,
        'motor_z': delta_z,
        'motor_cam': delta_cam,
        'actual_motor_target_x': actual_motor_target_x,
        'actual_motor_target_y': actual_motor_target_y
    }

# 如果作为独立程序运行，提供交互式测试功能
if __name__ == "__main__":
    print("===== 归一化坐标到物理坐标转换测试工具 =====")
    
    tank_dims = get_tank_dimensions()
    
    if tank_dims is None:
        print("无法获取鱼缸尺寸，请检查 Config_Move.yaml 文件。测试中止。")
        exit(1)

    print(f"鱼缸尺寸: {tank_dims[0]}mm x {tank_dims[1]}mm")
    print("\n===== 开始坐标测试 =====")
    print("(输入'q'退出程序)")

    while True:
        try:
            user_input = input("\n请输入归一化比例坐标 (norm_x norm_y): ")
            if user_input.strip().lower() == 'q':
                print("退出程序...")
                break

            coords = user_input.strip().split()
            if len(coords) != 2:
                print("错误: 请输入两个数值，用空格分隔")
                continue

            norm_x = float(coords[0])
            norm_y = float(coords[1])

            if not (0.0 <= norm_x <= 1.0 and 0.0 <= norm_y <= 1.0):
                print("警告: 输入的归一化坐标超出了 [0, 1] 范围，但仍会进行计算")

            physical_x, physical_y = normalized_to_physical_coordinates(norm_x, norm_y, tank_dims)

            if physical_x is not None and physical_y is not None:
                print(f"\n输入的归一化比例坐标: ({norm_x}, {norm_y})")
                print(f"转换后的物理坐标: ({physical_x:.2f}mm, {physical_y:.2f}mm)")
            else:
                print("坐标转换失败")

        except ValueError:
            print("错误: 请输入有效的数值")
        except Exception as e:
            print(f"发生错误: {str(e)}")
