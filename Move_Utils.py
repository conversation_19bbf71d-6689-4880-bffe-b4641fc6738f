# 合并的工具模块：包含CRC计算、串口管理、时间计算和配置加载功能
import serial
import yaml
import threading
import inspect
import datetime
import traceback
import copy
import os

# ==================== 全局串口锁及调试 ====================
serial_lock = threading.Lock()
LOCK_DEBUG = False  # 设置为 True 来开启锁调试打印，False 关闭

def get_timestamp():
    """获取当前时间戳字符串，格式为HH:MM:SS.mmm"""
    now = datetime.datetime.now()
    return now.strftime("%H:%M:%S.") + f"{now.microsecond//1000:03d}"

def trace_lock(action, lock_obj, func_name=None):
    """记录锁操作，用于调试"""
    _ = lock_obj  # 忽略未使用的参数
    if LOCK_DEBUG:
        if func_name is None:
            try:
                func_name = inspect.stack()[1].function
            except IndexError:
                func_name = "unknown_function"

        thread_id = threading.get_ident()
        # Simplified trace:
        print(f"[LOCK_TRACE] Thread-{thread_id} | {func_name} | Action: {action}")

def execute_with_serial_lock(func, serial_obj, lock_obj, *args, **kwargs):
    """
    使用串口锁执行函数的辅助函数
    
    Args:
        func: 要执行的函数
        serial_obj: 串口对象
        lock_obj: 锁对象
        *args, **kwargs: 传递给func的参数
        
    Returns:
        func的返回值
    """
    if not serial_obj or not serial_obj.is_open:
        print(f"[{get_timestamp()}] 错误: 串口不可用")
        return False
        
    trace_lock(f"Attempting to acquire for {func.__name__}", lock_obj)
    with lock_obj:
        trace_lock(f"Acquired for {func.__name__}", lock_obj)
        try:
            result = func(serial_obj, *args, **kwargs)
            return result
        finally:
            trace_lock(f"Released for {func.__name__}", lock_obj)

# ==================== 智能配置更新功能 ====================

# 全局变量用于跟踪配置文件的修改时间
_config_file_mtime_cache = {}

def get_file_modification_time(file_path):
    """
    获取文件的修改时间

    Args:
        file_path (str): 文件路径

    Returns:
        float: 文件修改时间戳，如果文件不存在返回 0
    """
    try:
        return os.path.getmtime(file_path)
    except (OSError, IOError):
        return 0

def should_update_config(config_path="Config_Move.yaml"):
    """
    检查配置文件是否需要更新

    Args:
        config_path (str): 配置文件路径

    Returns:
        bool: True 如果需要更新，False 如果不需要更新
    """
    current_mtime = get_file_modification_time(config_path)
    cached_mtime = _config_file_mtime_cache.get(config_path, 0)

    if current_mtime != cached_mtime:
        print(f"[{get_timestamp()}] 配置文件 {config_path} 修改时间变化: {cached_mtime} -> {current_mtime}")
        return True
    else:
        print(f"[{get_timestamp()}] 配置文件 {config_path} 未修改，跳过更新")
        return False

def update_config_mtime_cache(config_path="Config_Move.yaml"):
    """
    更新配置文件修改时间缓存

    Args:
        config_path (str): 配置文件路径
    """
    current_mtime = get_file_modification_time(config_path)
    _config_file_mtime_cache[config_path] = current_mtime
    print(f"[{get_timestamp()}] 已更新配置文件 {config_path} 的修改时间缓存: {current_mtime}")

def smart_update_dynamic_config(current_config, config_path="Config_Move.yaml"):
    """
    智能更新动态配置：只有在配置文件修改时间变化时才执行更新

    Args:
        current_config (dict): 当前正在使用的配置字典，将被修改。
        config_path (str): 配置文件的路径。

    Returns:
        tuple: (updated_config, was_updated)
               updated_config: 更新后的配置字典 (与传入的 current_config 是同一个对象)
               was_updated: bool, 表示是否实际执行了更新
    """
    if not should_update_config(config_path):
        return current_config, False

    print(f"[{get_timestamp()}] 配置文件已修改，开始执行动态配置更新...")
    updated_config = update_dynamic_config(current_config, config_path)

    # 更新修改时间缓存
    update_config_mtime_cache(config_path)

    return updated_config, True

def update_dynamic_config(current_config, config_path="Config_Move.yaml"):
    """
    从配置文件重新加载并更新动态参数。
    此函数会原地修改传入的 current_config 字典，并保证操作的原子性。

    Args:
        current_config (dict): 当前正在使用的配置字典，将被修改。
        config_path (str): 配置文件的路径。

    Returns:
        dict: 更新后的配置字典 (与传入的 current_config 是同一个对象)。
    """
    print(f"[{get_timestamp()}] 正在从 {config_path} 重新加载动态配置...")
    try:
        new_config = load_motor_config(config_path)
        if not new_config:
            print(f"[{get_timestamp()}] 警告: 无法加载新配置，将继续使用现有配置。")
            return current_config

        # 创建配置的深拷贝进行修改，以保证原子性。如果出错，原始配置不受影响。
        config_copy = copy.deepcopy(current_config)

        # 1. 要完全覆盖的顶级键
        keys_to_overwrite = [
            'global_settings', 'fish_tank', 'display_pos', 'catch_settings',
            'temp_height', 'camera_showcase_settings', 'release_settings', 'homing'
        ]
        for key in keys_to_overwrite:
            if key in new_config:
                config_copy[key] = new_config[key]  # 在副本上修改
                print(f"  - 已更新部分: {key}")
            else:
                print(f"  - 警告: 新配置中未找到 '{key}'，保留旧值。")

        # 2. 需要部分更新的电机参数
        motor_keys = ['motor_x', 'motor_y', 'motor_z', 'motor_cam']
        motor_params_to_update = ['speed', 'max_acceleration', 'max_distance', 'offset']

        for m_key in motor_keys:
            if m_key in new_config and m_key in config_copy:
                print(f"  - 正在更新 {m_key} 的参数:")
                for p_key in motor_params_to_update:
                    # 只在新配置中存在该参数时才更新
                    if p_key in new_config[m_key]:
                        # 检查值是否有变化
                        if config_copy[m_key].get(p_key) != new_config[m_key][p_key]:
                            config_copy[m_key][p_key] = new_config[m_key][p_key]  # 在副本上修改
                            print(f"    - {p_key}: {config_copy[m_key][p_key]}")
            else:
                 print(f"  - 警告: 新配置中未找到 '{m_key}'，跳过更新。")

        # 所有操作成功后，用更新后的副本替换原始配置
        current_config.clear()
        current_config.update(config_copy)

        print(f"[{get_timestamp()}] 动态配置更新完成。")
        return current_config

    except Exception as e:
        print(f"[{get_timestamp()}] 错误: 更新动态配置时发生异常，配置未被修改: {e}")
        traceback.print_exc()
        return current_config  # 返回未被修改的原始配置

# ==================== 配置加载功能 ====================

def load_motor_config(config_path="Config_Move.yaml"):
    """
    加载电机和串口配置信息
    
    Args:
        config_path: 配置文件路径
        
    Returns:
        包含配置信息的字典
    """
    try:
        with open(config_path, "r", encoding="utf-8") as config_file:
            config = yaml.safe_load(config_file)
        return config
    except Exception as e:
        print(f"加载配置文件失败: {str(e)}")
        return None

# ==================== CRC计算功能 ====================

def crc16(data: bytes) -> bytes:
    """
    计算Modbus CRC16校验码
    
    Args:
        data: 要计算校验码的字节序列
        
    Returns:
        CRC16校验码（小端序）
    """
    crc = 0xFFFF
    for byte in data:
        crc ^= byte
        for _ in range(8):
            if crc & 0x0001:
                crc = (crc >> 1) ^ 0xA001
            else:
                crc >>= 1
    return crc.to_bytes(2, byteorder='little')

# ==================== 串口管理功能 ====================

def open_serial(port_config):
    """
    根据配置打开串口连接
    
    Args:
        port_config: 包含串口配置的字典
        
    Returns:
        串口对象
    """
    try:
        ser = serial.Serial(
            port=port_config['port'],
            baudrate=port_config['baudrate'],
            bytesize=serial.EIGHTBITS,
            parity=serial.PARITY_NONE,
            stopbits=serial.STOPBITS_ONE,
            timeout=port_config['timeout']
        )
        return ser
    except Exception as e:
        print(f"打开串口失败: {str(e)}")
        return None

def close_serial(ser):
    """
    安全关闭串口连接
    
    Args:
        ser: 要关闭的串口对象
    """
    if ser and ser.is_open:
        ser.close()

# ==================== 时间计算功能 ====================

def calculate_accel_time(lead, speed, max_acceleration):
    """
    计算加速时间（秒）
    
    Args:
        lead: 导程（毫米/转）
        speed: 速度（rpm）
        max_acceleration: 最大加速度（米/秒²）
        
    Returns:
        加速时间（秒）
    """
    # 将速度转换单位为米每秒
    speed_rps = (speed / 60) * (lead / 1000)  # m/s
    # 加速度为米每平方秒
    if max_acceleration <= 0:
        return 1.0  # 防止除零错误，设置默认值 1 秒
    accel_time = speed_rps / max_acceleration  # 单位是秒
    return max(accel_time, 0.1)  # 确保至少 100 毫秒


def estimate_motion_time(distance_mm, lead, speed, max_acceleration):
    """
    估算运动时间（秒）
    
    Args:
        distance_mm: 移动距离（毫米）
        lead: 导程（毫米/转）
        speed: 速度（rpm）
        max_acceleration: 最大加速度（米/秒²）
        
    Returns:
        估算的总运动时间（秒）
    """
    speed_positive = abs(speed)
    # 将速度转换为米每秒
    speed_rps = (speed_positive / 60) * (lead / 1000)  # 米每秒
    max_accel = max_acceleration  # 米每平方秒

    # 防止速度为零导致的除零错误
    if speed_rps < 0.0001:  # 使用很小的阈值而不是直接检查等于零
        print(f"警告: 速度({speed} RPM)过低，可能导致计算错误。将使用最小速度值。")
        speed_rps = 0.0001  # 设置一个最小速度限制
        speed_positive = (speed_rps * 60 * 1000) / lead  # 反向计算RPM值供显示用
    
    # 计算加速和减速阶段的时间
    accel_time = speed_rps / max_accel if max_accel > 0 else 1.0  # 防止除零错误
    decel_time = accel_time  # 减速时间通常等于加速时间（秒）
    s_acc = 0.5 * max_accel * accel_time**2  # 加速位移（米）
    s_dec = s_acc  # 减速位移（米）
    total_distance = abs(distance_mm) / 1000  # 转换为米

    # 打印详细计算过程和物理量转换
    print(f"\n--- 运动时间估算详情 ---")
    print(f"导程: {lead} mm/转")
    print(f"目标速度: {speed} RPM = {speed / 60:.4f} 转/秒 = {speed_rps:.4f} m/s")
    print(f"加速度: {max_accel} m/s²")
    print(f"加速时间: {accel_time:.4f}秒")
    print(f"减速时间: {decel_time:.4f}秒")
    print(f"加速位移: {s_acc:.4f} m")
    print(f"减速位移: {s_dec:.4f} m")
    print(f"总位移: {total_distance:.4f} m")

    if total_distance <= (s_acc + s_dec):
        # 来不及达到最高速度的情况
        # 在这种情况下需要重新计算加速和减速时间
        peak_speed = (2 * max_accel * total_distance) ** 0.5  # 峰值速度（m/s）
        adjusted_accel_time = peak_speed / max_accel if max_accel > 0 else 1.0  # 防止除零错误
        time_total = 2 * adjusted_accel_time  # 总时间 = 加速 + 减速（不含匀速段）
        print(f"情况: 未达到最高速度，三角形速度曲线")
        print(f"峰值速度: {peak_speed:.4f} m/s = {peak_speed * 60 / (lead/1000):.1f} RPM")
        print(f"调整后加减速时间: {adjusted_accel_time:.4f}秒 x 2")
        print(f"估算时间: {time_total:.4f}秒")
    else:
        # 能够达到最高速度的情况
        s_const = total_distance - (s_acc + s_dec)  # 匀速位移
        # 防止除零错误
        t_const = s_const / speed_rps  # 匀速时间
        time_total = accel_time + t_const + decel_time
        print(f"情况: 达到最高速度，梯形速度曲线")
        print(f"加速时间: {accel_time:.4f}秒")
        print(f"匀速距离: {s_const:.4f}m")
        print(f"匀速时间: {t_const:.4f}秒")
        print(f"减速时间: {decel_time:.4f}秒")
        print(f"总估算时间: {time_total:.4f}秒 (加速+匀速+减速)")
    
    print("----------------------")
    return time_total


def estimate_motion_time_cam(distance_mm, lead, speed, max_accel_fast, max_accel_slow):
    """
    CAM轴专用：估算运动时间（正向加速/负向减速用fast，正向减速/负向加速用slow）
    """
    speed_positive = abs(speed)
    speed_rps = (speed_positive / 60) * (lead / 1000)  # m/s
    total_distance = abs(distance_mm) / 1000  # m

    is_positive = distance_mm > 0  # 正向（向下）

    # 加速/减速阶段加速度选择
    if is_positive:
        accel = max_accel_fast
        decel = max_accel_slow
    else:
        accel = max_accel_slow
        decel = max_accel_fast

    if accel <= 0: accel = 0.1
    if decel <= 0: decel = 0.1

    accel_time = speed_rps / accel
    decel_time = speed_rps / decel
    s_acc = 0.5 * accel * accel_time ** 2
    s_dec = 0.5 * decel * decel_time ** 2

    if total_distance <= (s_acc + s_dec):
        avg_accel = (accel + decel) / 2
        peak_speed = (2 * avg_accel * total_distance) ** 0.5
        t_acc = peak_speed / accel
        t_dec = peak_speed / decel
        return t_acc + t_dec
    else:
        s_const = total_distance - (s_acc + s_dec)
        t_const = s_const / speed_rps
        return accel_time + t_const + decel_time

def calculate_motion_times(axis_distances, config):
    """
    计算各个轴的运动时间以及总的运动时间，并计算同步移动时间
    CAM轴支持双加速度
    """
    axis_times = {}
    print("\n--- 各轴运动时间估算 ---")
    
    global_max_speed = config.get('global_settings', {}).get('max_speed_rpm', float('inf'))
    print(f"全局最大速度限制: {global_max_speed} RPM")
    
    for axis_key, distance in axis_distances.items():
        if distance == 0:
            axis_times[axis_key] = 0.0
            print(f"{axis_key}: 不移动，时间为0")
            continue
            
        if axis_key in config:
            motor_config = config[axis_key]
            lead = motor_config['lead']
            speed = motor_config['speed']
            if speed > global_max_speed:
                print(f"{axis_key}速度 {speed} RPM 超过限制，将使用 {global_max_speed} RPM")
                speed = global_max_speed

            if axis_key == 'motor_cam':
                max_accel_fast = motor_config.get('max_acceleration_fast', 0.5)
                max_accel_slow = motor_config.get('max_acceleration_slow', 0.5)
                print(f"\n计算{axis_key}轴（双加速度），距离={distance}mm:")
                time_required = estimate_motion_time_cam(distance, lead, speed, max_accel_fast, max_accel_slow)
            else:
                max_acceleration = motor_config['max_acceleration']
                print(f"\n计算{axis_key}轴，距离={distance}mm:")
                time_required = estimate_motion_time(distance, lead, speed, max_acceleration)
            axis_times[axis_key] = time_required
            print(f"{axis_key}估算时间: {time_required:.3f}秒")
        else:
            axis_times[axis_key] = 0.0
            print(f"{axis_key}: 配置缺失，时间默认为0")
    
    # 计算X、Y、Z轴中最长的时间作为同步时间
    synced_axes = ['motor_x', 'motor_y', 'motor_z']
    synced_times = [axis_times.get(axis, 0.0) for axis in synced_axes]
    synced_distances = [abs(axis_distances.get(axis, 0.0)) for axis in synced_axes]
    
    # 只考虑需要移动的轴（距离不为0）
    moving_synced_times = [t for i, t in enumerate(synced_times) if synced_distances[i] > 0]
    sync_time = max(moving_synced_times) if moving_synced_times else 0.0
    
    print(f"\n--- 同步移动时间计算 ---")
    print(f"X轴时间: {axis_times.get('motor_x', 0.0):.3f}秒")
    print(f"Y轴时间: {axis_times.get('motor_y', 0.0):.3f}秒")
    print(f"Z轴时间: {axis_times.get('motor_z', 0.0):.3f}秒")
    print(f"同步移动时间: {sync_time:.3f}秒")
    
    # 计算调整后的各轴速度，使它们在相同时间内完成移动
    adjusted_speeds = {}
    for axis in synced_axes:
        if axis_distances.get(axis, 0.0) == 0 or sync_time == 0:
            # 如果轴不需要移动，保持原来的速度设置
            adjusted_speeds[axis] = config[axis]['speed']
            continue
        
        distance = abs(axis_distances[axis])
        lead = config[axis]['lead']
        max_acceleration = config[axis]['max_acceleration']
        if sync_time > 0:
            accel_time_base = (config[axis]['speed'] / 60 * (lead / 1000)) / max_acceleration
            if 2 * accel_time_base >= sync_time:
                peak_speed_mps = (max_acceleration * sync_time) / 2
                adjusted_speed_rpm = peak_speed_mps * 60 / (lead / 1000)
            else:
                distance_m = distance / 1000
                a = 1.0 / max_acceleration
                b = -sync_time
                c = distance_m
                discriminant = b**2 - 4 * a * c
                if discriminant < 0:
                    adjusted_speed_rpm = config[axis]['speed']
                    print(f"{axis}轴: 调整速度计算错误，使用原始速度 {adjusted_speed_rpm} RPM")
                else:
                    speed_mps = (-b - (discriminant)**0.5) / (2 * a)
                    adjusted_speed_rpm = speed_mps * 60 / (lead / 1000)
                    max_speed_rpm_config = config[axis]['speed']
                    effective_max_speed_rpm = min(max_speed_rpm_config, global_max_speed)
                    if adjusted_speed_rpm > effective_max_speed_rpm:
                        print(f"{axis}轴: 计算同步速度 {adjusted_speed_rpm:.1f} RPM 超过有效最大限制 {effective_max_speed_rpm:.1f} RPM，将使用限制值。")
                        adjusted_speed_rpm = effective_max_speed_rpm
            if adjusted_speed_rpm < 1.0:
                print(f"警告: {axis}轴计算出的同步速度({adjusted_speed_rpm:.4f} RPM)过低")
                print(f"DEBUG参数: 距离={distance}mm, 同步时间={sync_time}s, 加速度={max_acceleration}m/s²")
                adjusted_speed_rpm = max(1.0, adjusted_speed_rpm)
                print(f"已将{axis}轴速度调整为最小值: {adjusted_speed_rpm} RPM")
            if axis == 'motor_z':
                compensation_factor = config.get('motor_z', {}).get('sync_speed_compensation_factor', 1.0)
                if compensation_factor != 1.0:
                    original_calculated_speed_z = adjusted_speed_rpm
                    adjusted_speed_rpm *= compensation_factor
                    print(f"Z轴: 应用同步速度补偿系数 {compensation_factor:.2f}。速度从 {original_calculated_speed_z:.1f} RPM 调整为 {adjusted_speed_rpm:.1f} RPM。")
                    max_speed_rpm_config_z = config['motor_z']['speed']
                    effective_max_speed_rpm_z = min(max_speed_rpm_config_z, global_max_speed)
                    if adjusted_speed_rpm > effective_max_speed_rpm_z:
                        print(f"Z轴: 补偿后速度 {adjusted_speed_rpm:.1f} RPM 超过有效最大限制 {effective_max_speed_rpm_z:.1f} RPM，将使用限制值。")
                        adjusted_speed_rpm = effective_max_speed_rpm_z
                    if adjusted_speed_rpm < 1.0:
                        adjusted_speed_rpm = 1.0
                        print(f"Z轴: 补偿后速度过低，已调整为最小值: 1.0 RPM")
            adjusted_speeds[axis] = adjusted_speed_rpm
            print(f"{axis}轴: 调整速度为 {adjusted_speed_rpm:.1f} RPM 以达到同步时间 {sync_time:.3f}秒")
    cam_speed_config = config['motor_cam']['speed']
    adjusted_speeds['motor_cam'] = min(cam_speed_config, global_max_speed)
    if axis_distances.get('motor_cam', 0.0) != 0:
        print(f"CAM轴: 速度为 {adjusted_speeds['motor_cam']} RPM (不同步" +
              (", 已受全局限速" if adjusted_speeds['motor_cam'] < cam_speed_config else "") + ")")
    z_axis_time = axis_times.get('motor_z', 0.0)
    max_time = max(axis_times.values()) if axis_times else 0.0
    
    print(f"\n所有轴时间结果:")
    for axis, t in axis_times.items():
        print(f" - {axis}: {t:.3f}秒")
    print(f"Z轴时间: {z_axis_time:.3f}秒")
    print(f"最长运动时间: {max_time:.3f}秒")
    print(f"同步移动时间: {sync_time:.3f}秒")
    
    # DEBUG: 检查最终的速度值
    print(f"\nDEBUG - 最终调整后的速度值:")
    for axis, speed in adjusted_speeds.items():
        print(f"{axis}: {speed:.3f} RPM")
    print("------------------------")
    
    return z_axis_time, max_time, sync_time, adjusted_speeds
