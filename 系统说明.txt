完整梳理整个系统的文件关系和用途：
首先是运动相关程序说明：
## 完整的文件关系图

```
Move_DetectMain.py (主程序)
├── Move_Homing.py (回零操作)
│   ├── Move_Serial.py
│   ├── Move_ConfigLoader.py  
│   ├── Move_commands.py
│   ├── Move_CRC.py
│   └── Move_huakong_relay.py
├── Move_Control.py (电机控制核心)
│   ├── Move_Serial.py
│   ├── Move_ConfigLoader.py
│   ├── Move_commands.py
│   │   ├── Move_CRC.py
│   │   └── Move_TimeCalc.py
│   ├── Move_TimeCalc.py
│   └── Move_huakong_relay.py
├── Move_ConfigLoader.py (配置加载)
├── Detect_Client.py (YOLO检测客户端)
├── Detect_Coordinate.py (坐标转换)
│   ├── cam_pos.yaml
│   ├── yolo_config.yaml
│   └── MotorConfig.yaml
├── Move_Huakong_Discr_Input.py (数字量输入检测)
│   ├── Move_Serial.py
│   └── Move_CRC.py
├── Move_ReleasePos.py (投放位置计算)
│   └── Move_ConfigLoader.py
└── Move_Serial.py (串口管理)
```

## 主程序 Move_DetectMain.py 的核心功能

这是一个完整的**YOLO智能识别与电机定位系统**，主要功能包括：

### 1. 系统初始化
- 加载配置文件（电机参数、坐标转换等）
- 设置坐标变换矩阵
- 启动YOLO检测服务
- 执行所有轴回零操作

### 2. 物体检测与显示
- 实时获取YOLO检测结果
- 将像素坐标转换为物理坐标
- 显示检测到的物体列表（编号、类别、坐标）

### 3. 智能抓取流程
完整的15步抓取流程：
1. 打开爪子
2. 移动XY轴到目标位置
3. Z轴下降（根据物体类别调整高度）
4. 闭合爪子抓取
5. 等待抓取稳定
6. Z轴上升到临时高度
7. 检查爪子状态（判断是否抓取成功）
8. 临时高度停留
9. 移动到展示位置
10. 展示位置停留
11. 计算最佳投放位置
12. 移动到投放位置
13. 打开爪子释放物体
14. 等待释放稳定
15. 回到初始位置

### 4. 智能投放位置计算
- 获取当前所有物体位置
- 使用算法计算最佳投放点（避开已有物体）
- 考虑鱼缸边界和安全距离

## 各文件在主程序中的具体作用

### 检测相关模块
- **Detect_Client.py**: 与YOLO检测服务通信，获取物体检测结果
- **Detect_Coordinate.py**: 处理像素坐标到物理坐标的转换

### 运动控制模块
- **Move_Control.py**: 执行四轴电机的协调运动
python Move_Control.py <x_distance> <y_distance> <z_distance> <cam_distance> [do2_state]
- **Move_commands.py**: 发送具体的电机控制指令
- **Move_TimeCalc.py**: 计算运动时间，实现多轴同步

### 硬件接口模块
- **Move_huakong_relay.py**: 控制气动爪子的开合（DO1/DO2）
- **Move_Huakong_Discr_Input.py**: 检测爪子状态，判断是否抓取到物体

### 辅助功能模块
- **Move_Homing.py**: 系统启动时的回零操作
python Move_Homing.py [--axis AXIS] [--config CONFIG_FILE]
# 示例1：回零所有轴（默认行为）
python Move_Homing.py
--axis motor_z
--axis motor_x
--axis motor_y
# 示例5：只回零CAM轴
python Move_Homing.py --axis motor_cam
# 示例7：回零特定轴并使用自定义配置
python Move_Homing.py --axis motor_z --config "TestConfig.yaml"
- **Move_ReleasePos.py**: 计算避开障碍物的最佳投放位置
- **Move_Serial.py**: 统一的串口通信管理
- **Move_CRC.py**: Modbus通信的校验计算

## 主程序的工作流程

1. **系统启动**: 配置加载 → 坐标系统初始化 → YOLO服务启动 → 所有轴回零
2. **实时检测**: 持续获取物体检测结果并显示
3. **用户交互**: 通过编号选择要抓取的物体
4. **智能抓取**: 执行完整的抓取-展示-投放流程
5. **状态管理**: 实时跟踪各轴位置，考虑轴偏移值
6. **异常处理**: 检测抓取失败，提供错误恢复机制

这是一个高度集成的智能机械臂系统，结合了计算机视觉、运动控制、传感器反馈和智能决策算法。
————————————————————————————————————————
下面是检测相关程序说明；
## 文件功能和关系树形图

```
主要程序入口
├── Detect_Client.py [独立运行] - 检测服务客户端
│   ├── 输入: 命令行参数 (start/stop/get/demo)
│   ├── 输出: 服务状态、检测结果、FPS统计
│   └── 引用: Detect_BaseService.py (启动服务)
│
├── Detect_BaseService.py [独立运行] - 检测服务基类
│   ├── 输入: 摄像头视频流
│   ├── 输出: 网络服务监听、终端显示、可视化窗口
│   ├── 引用关系:
│   │   ├── Detect_YOLOV8seg.py (分割模型)
│   │   ├── Detect_YoloV10Detc.py (检测模型) 
│   │   └── Detect_ROI.py (ROI处理)
│   └── 被引用: Detect_Client.py
│
具体检测实现
├── Detect_YOLOV8seg.py [独立运行] - YOLOv8分割检测
│   ├── 输入: 摄像头ID、窗口显示选项
│   ├── 输出: 实时分割检测结果、掩码标注
│   ├── 继承: Detect_BaseService.py
│   └── 引用: Detect_ROI.py
│
├── Detect_YoloV10Detc.py [独立运行] - YOLOv10目标检测  
│   ├── 输入: 摄像头ID、窗口显示选项
│   ├── 输出: 实时目标检测结果、边界框标注
│   ├── 继承: Detect_BaseService.py
│   └── 引用: Detect_ROI.py
│
工具模块
├── Detect_ROI.py [工具模块] - ROI区域处理
│   ├── 功能: ROI掩码创建、坐标变换、检测过滤
│   └── 被引用: 所有检测实现文件
│
├── Detect_Coordinate.py [独立运行] - 坐标转换工具
│   ├── 输入: 相对像素坐标 (交互式输入)
│   ├── 输出: 物理坐标(毫米)、转换测试结果
│   └── 引用: 三个配置文件
│
配置文件
├── Config_Detect.yaml - YOLO检测配置
├── Config_Detect_Cam.yaml - 摄像头和ROI配置  
└── Config_Move.yaml - 电机和物理尺寸配置
```

## 可独立运行的程序

### 1. Detect_Client.py (主要客户端)
```bash
# 启动检测服务
python Detect_Client.py start

# 获取单次检测结果  
python Detect_Client.py get

# 持续获取检测结果
python Detect_Client.py get --continuous --interval 1.0

# 停止检测服务
python Detect_Client.py stop

# 演示模式
python Detect_Client.py demo --duration 30
```
**输出**: 服务状态信息、检测结果(物体编号、类别、坐标、置信度)、FPS统计

### 2. Detect_BaseService.py (服务基类)
```bash
# 作为服务运行(无窗口)
python Detect_BaseService.py --as-service

# 带可视化窗口运行
python Detect_BaseService.py

# 指定参数运行
python Detect_BaseService.py --camera 0 --port 5555 --as-service
```
**输出**: 网络服务监听、终端显示检测结果、可视化窗口(可选)

### 3. Detect_YOLOV8seg.py (分割检测)
```bash
python Detect_YOLOV8seg.py --camera 0
python Detect_YOLOV8seg.py --no-window  # 无窗口模式
```
**输出**: 实时分割检测结果、掩码标注、窗口显示

### 4. Detect_YoloV10Detc.py (目标检测)
```bash
python Detect_YoloV10Detc.py --camera 0  
python Detect_YoloV10Detc.py --no-window  # 无窗口模式
```
**输出**: 实时目标检测结果、边界框标注、窗口显示

### 5. Detect_Coordinate.py (坐标转换测试)
```bash
python Detect_Coordinate.py
```
**输入**: 交互式输入相对像素坐标 (x y)
```
请输入相对像素坐标 (x y): 100 200
```
**输出**: 
```
输入的相对像素坐标: (100.0, 200.0)
转换后的物理坐标: (25.5mm, -45.2mm)
```

## 核心工作流程

1. **配置阶段**: 系统读取3个YAML配置文件
2. **服务启动**: 通过Client启动BaseService，BaseService根据配置选择检测模型
3. **检测处理**: 摄像头 → ROI处理 → YOLO推理 → 对象跟踪 → 坐标转换  
4. **结果输出**: 网络接口提供检测结果，客户端获取并显示
5. **坐标转换**: 可独立测试像素坐标到物理坐标的转换

## 依赖关系总结

- **Detect_ROI.py**: 被所有检测实现引用的工具模块
- **Detect_BaseService.py**: 核心基类，被具体检测实现继承
- **配置文件**: 被所有模块读取，定义系统参数
- **Detect_Client.py**: 最上层的用户接口，管理整个检测服务