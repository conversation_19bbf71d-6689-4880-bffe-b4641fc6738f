"""
计算物体最佳投放位置

该模块用于计算鱼缸内投放物体的最佳位置，通过两步搜索算法：
1. 粗略搜索：按较大网格划分鱼缸，寻找距离所有已知物体最远的位置
2. 精细搜索：在粗略搜索结果周围进行小网格搜索，进一步优化位置

注意：现在坐标系已更新，X和Y方向直接对应物理世界的方向
"""

import numpy as np
import yaml
from Move_Utils import load_motor_config

def load_release_settings(config_object): # MODIFIED: parameter name and logic
    """加载投放设置配置"""
    # REMOVED: config = load_motor_config(config_path)
    if not config_object or 'release_settings' not in config_object:
        # 使用默认配置
        print("警告: 未找到投放设置或配置对象无效，使用默认值")
        return {
            'release_height_z': 300.0, # Default, ensure it matches your needs
            'release_time': 0.5,
            'safe_margin': 50.0,
            'rough_grid': 100.0,
            'fine_grid': 10.0
        }
    
    # 从已加载的配置对象中获取配置
    release_settings = config_object['release_settings']
    
    # 确保配置键一致性（支持下划线和连字符两种写法）
    if 'rough-grid' in release_settings and 'rough_grid' not in release_settings:
        release_settings['rough_grid'] = release_settings['rough-grid']
    if 'fine-grid' in release_settings and 'fine_grid' not in release_settings:
        release_settings['fine_grid'] = release_settings['fine-grid']
    
    return release_settings

def get_tank_dimensions(config_object): # MODIFIED: parameter name and logic
    """获取鱼缸尺寸"""
    # REMOVED: config = load_motor_config(config_path)
    if not config_object or 'fish_tank' not in config_object:
        # 使用默认尺寸
        print("警告: 未找到鱼缸尺寸配置或配置对象无效，使用默认值")
        return 600.0, 600.0
    
    tank_x = config_object['fish_tank'].get('tank_x', 600.0) # MODIFIED: tank_X to tank_x to match YAML
    tank_y = config_object['fish_tank'].get('tank_y', 600.0)
    
    return tank_x, tank_y

def calculate_min_distance_to_objects(point, objects):
    """
    计算点到所有物体的最小距离
    
    Args:
        point: (x, y) 坐标元组
        objects: 物体列表，每个物体必须有 'physical_coords' 属性，值为 (x, y) 坐标
        
    Returns:
        点到所有物体的最小距离
    """
    if not objects:
        return float('inf')  # 如果没有物体，返回无穷大
    
    x, y = point
    min_distance = float('inf')
    
    for obj in objects:
        obj_x, obj_y = obj['physical_coords']
        distance = np.sqrt((x - obj_x)**2 + (y - obj_y)**2)
        min_distance = min(min_distance, distance)
    
    return min_distance

def find_optimal_release_position(objects, config_input="Config_Move.yaml"): # MODIFIED: parameter name for clarity
    """
    找到最佳投放位置
    
    Args:
        objects: 物体列表，每个物体必须有 'physical_coords' 属性，值为 (x, y) 坐标
        config_input: 配置文件路径 (str) 或已加载的配置字典 (dict)
        
    Returns:
        (optimal_x, optimal_y) 元组，表示最佳投放位置
    """
    
    resolved_config = None
    if isinstance(config_input, str): # Input is a path
        resolved_config = load_motor_config(config_input)
        if not resolved_config:
            print(f"警告: 从路径 {config_input} 加载配置失败。投放位置计算可能使用默认值。")
    elif isinstance(config_input, dict): # Input is already a config object
        resolved_config = config_input
    else:
        print(f"错误: find_optimal_release_position 收到无效的配置输入类型: {type(config_input)}。将尝试使用默认值。")
        # Attempt to load default path as a fallback if necessary, or let resolved_config remain None
        # For safety, if type is wrong, resolved_config remains None, leading to defaults in helper functions.

    # 加载配置 (now using the resolved_config object)
    settings = load_release_settings(resolved_config)
    tank_x, tank_y = get_tank_dimensions(resolved_config)
    
    safe_margin = settings.get('safe_margin', 50.0)
    rough_grid = settings.get('rough_grid', 100.0)
    fine_grid = settings.get('fine_grid', 10.0)
    
    print(f"\n===== 计算最佳投放位置 =====")
    print(f"鱼缸尺寸: {tank_x} x {tank_y} mm")
    print(f"安全边距: {safe_margin} mm")
    print(f"粗网格大小: {rough_grid} mm")
    print(f"细网格大小: {fine_grid} mm")
    print(f"检测到的物体数量: {len(objects)}")
    
    # 如果没有检测到物体，返回鱼缸中心点
    if not objects:
        center_x = tank_x / 2
        center_y = tank_y / 2
        print(f"未检测到物体，使用鱼缸中心点 ({center_x:.1f}, {center_y:.1f}) 作为投放位置")
        return center_x, center_y
    
    # 定义有效区域（考虑安全边距）
    valid_x_min = safe_margin
    valid_x_max = tank_x - safe_margin
    valid_y_min = safe_margin
    valid_y_max = tank_y - safe_margin
    
    # 1. 粗略搜索
    print("开始粗略搜索...")
    best_point = None
    max_min_distance = -1
    
    # 在有效区域内创建粗网格点
    x_points = np.arange(valid_x_min, valid_x_max + 1, rough_grid)
    y_points = np.arange(valid_y_min, valid_y_max + 1, rough_grid)
    
    for x in x_points:
        for y in y_points:
            point = (x, y)
            min_distance = calculate_min_distance_to_objects(point, objects)
            
            if min_distance > max_min_distance:
                max_min_distance = min_distance
                best_point = point
    
    if best_point is None:
        # 如果搜索失败，使用鱼缸中心
        best_point = (tank_x / 2, tank_y / 2)
    
    rough_best_x, rough_best_y = best_point
    print(f"粗略搜索结果: ({rough_best_x:.1f}, {rough_best_y:.1f}), 最小距离: {max_min_distance:.1f} mm")
    
    # 2. 精细搜索 - 在粗略最佳点周围搜索
    print("开始精细搜索...")
    fine_x_min = max(valid_x_min, float(rough_best_x - rough_grid))
    fine_x_max = min(valid_x_max, float(rough_best_x + rough_grid))
    fine_y_min = max(valid_y_min, float(rough_best_y - rough_grid))
    fine_y_max = min(valid_y_max, float(rough_best_y + rough_grid))
    
    fine_x_points = np.arange(fine_x_min, fine_x_max + 1, fine_grid)
    fine_y_points = np.arange(fine_y_min, fine_y_max + 1, fine_grid)
    
    max_min_distance = -1
    optimal_point = best_point
    
    for x in fine_x_points:
        for y in fine_y_points:
            point = (x, y)
            min_distance = calculate_min_distance_to_objects(point, objects)
            
            if min_distance > max_min_distance:
                max_min_distance = min_distance
                optimal_point = point
    
    optimal_x, optimal_y = optimal_point
    print(f"精细搜索结果: ({optimal_x:.1f}, {optimal_y:.1f}), 最小距离: {max_min_distance:.1f} mm")
    print("===== 最佳投放位置计算完成 =====")
    
    return optimal_x, optimal_y

# 测试用主函数
if __name__ == "__main__":
    # 创建一些测试物体
    test_objects = [
        {'physical_coords': (100, 100)},
        {'physical_coords': (500, 500)},
        {'physical_coords': (200, 400)}
    ]
    
    # 寻找最佳投放位置
    # The default "Config_Move.yaml" will be used by find_optimal_release_position
    # if it's in the same directory or accessible via default path logic in load_motor_config.
    optimal_x, optimal_y = find_optimal_release_position(test_objects)
    print(f"\n最佳投放位置: X = {optimal_x:.1f} mm, Y = {optimal_y:.1f} mm")

    # Example of passing a loaded config for testing (optional, if needed for specific test scenarios)
    # test_config_data = load_motor_config("Config_Move.yaml")
    # if test_config_data:
    #     optimal_x_loaded, optimal_y_loaded = find_optimal_release_position(test_objects, test_config_data)
    #     print(f"最佳投放位置 (使用预加载配置): X = {optimal_x_loaded:.1f} mm, Y = {optimal_y_loaded:.1f} mm")
    # else:
    #     print("无法加载 Config_Move.yaml 进行预加载测试。")
