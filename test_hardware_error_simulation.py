#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
硬件错误模拟测试程序
测试Move_commands错误检测、Move_Control急停响应、Move_main错误上报和网络发送的完整流程
"""

import time
import threading
import json
import socket
import datetime
import random
import copy
from unittest.mock import Mock, patch, MagicMock
import sys
import os

# 添加当前目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

# 导入需要测试的模块
from Move_Utils import load_motor_config
from Move_Control import run_motor_commands
from Move_Main import process_movement
import Move_commands
import move_network

def get_timestamp():
    """获取当前时间戳字符串，格式为HH:MM:SS.mmm"""
    now = datetime.datetime.now()
    return now.strftime("%H:%M:%S.") + f"{now.microsecond//1000:03d}"

class HardwareErrorSimulator:
    """硬件错误模拟器"""
    
    def __init__(self):
        self.test_results = {}
        self.mock_serial = None
        self.error_injection_active = False
        self.current_error_type = None
        self.original_functions = {}
        
    def setup_mock_serial(self):
        """设置模拟串口"""
        self.mock_serial = Mock()
        self.mock_serial.is_open = True
        self.mock_serial.write = Mock()
        self.mock_serial.read = Mock(return_value=b'\x01\x06\x00\x4E\x00\x00\x00\x00')  # 模拟正常响应
        self.mock_serial.close = Mock()
        self.mock_serial.in_waiting = 8  # 模拟有8字节数据等待读取
        self.mock_serial.timeout = 1.0

        # 模拟send_modbus_command的响应
        def mock_send_command(*args, **kwargs):
            return True

        # 模拟CRC计算
        def mock_crc(*args, **kwargs):
            return b'\x00\x00'
        
    def inject_error_1_cumulative_reverse_movement(self):
        """错误1: 累计反向位移超限"""
        print(f"[{get_timestamp()}] 注入错误1: 累计反向位移超限")

        # 模拟位置读取函数，返回反向移动的位置数据
        original_read_position = Move_commands.read_motor_position

        def mock_read_position(ser, slave_addr, serial_lock):
            if self.error_injection_active and self.current_error_type == 1:
                # 模拟反向移动：目标正向10mm，但实际位置逐渐变为负值
                static_counter = getattr(mock_read_position, 'counter', 0)
                mock_read_position.counter = static_counter + 1

                # 初始位置0，然后逐渐变为负值，累计超过容差
                if static_counter < 3:
                    return 0  # 初始位置
                else:
                    # 每次调用返回更负的值，模拟累计反向位移
                    # 假设脉冲/转=1000，导程=1mm，则1000脉冲=1mm
                    return -1500 * (static_counter - 2)  # 脉冲数，对应负向移动
            return original_read_position(ser, slave_addr, serial_lock)

        Move_commands.read_motor_position = mock_read_position
        self.original_functions['read_motor_position'] = original_read_position
        
    def inject_error_2_wrong_direction(self):
        """错误2: 运动方向错误"""
        print(f"[{get_timestamp()}] 注入错误2: 运动方向错误")
        
        original_read_position = Move_commands.read_motor_position
        
        def mock_read_position(ser, slave_addr, serial_lock):
            if self.error_injection_active and self.current_error_type == 2:
                # 目标正向，但立即返回大负值
                return -5000  # 对应-5mm左右的负向位移
            return original_read_position(ser, slave_addr, serial_lock)
        
        Move_commands.read_motor_position = mock_read_position
        self.original_functions['read_motor_position'] = original_read_position
        
    def inject_error_3_exceed_tolerance_during_motion(self):
        """错误3: 运动中超出容差"""
        print(f"[{get_timestamp()}] 注入错误3: 运动中超出容差")
        
        original_read_position = Move_commands.read_motor_position
        
        def mock_read_position(ser, slave_addr, serial_lock):
            if self.error_injection_active and self.current_error_type == 3:
                # 目标10mm，但返回超出容差的位置（假设容差1mm，返回12mm对应的脉冲）
                return 12000  # 对应12mm的位移，超出10mm+1mm容差
            return original_read_position(ser, slave_addr, serial_lock)
        
        Move_commands.read_motor_position = mock_read_position
        self.original_functions['read_motor_position'] = original_read_position
        
    def inject_error_4_delayed_sampling_deviation(self):
        """错误4: 延迟采样偏差超限"""
        print(f"[{get_timestamp()}] 注入错误4: 延迟采样偏差超限")
        
        original_read_position = Move_commands.read_motor_position
        
        def mock_read_position(ser, slave_addr, serial_lock):
            if self.error_injection_active and self.current_error_type == 4:
                # 运动过程中正常，但延迟采样时偏差过大
                static_counter = getattr(mock_read_position, 'counter', 0)
                mock_read_position.counter = static_counter + 1
                
                if static_counter < 10:  # 运动过程中
                    return 10000  # 正常到达目标位置10mm
                else:  # 延迟采样时
                    return 13000  # 偏差过大，13mm
            return original_read_position(ser, slave_addr, serial_lock)
        
        Move_commands.read_motor_position = mock_read_position
        self.original_functions['read_motor_position'] = original_read_position
        
    def inject_error_5_status_read_failure(self):
        """错误5: 读取状态失败"""
        print(f"[{get_timestamp()}] 注入错误5: 读取状态失败")
        
        original_read_status = Move_commands.read_motor_status
        
        def mock_read_status(ser, slave_addr, serial_lock):
            if self.error_injection_active and self.current_error_type == 5:
                return None  # 模拟读取失败
            return original_read_status(ser, slave_addr, serial_lock)
        
        Move_commands.read_motor_status = mock_read_status
        self.original_functions['read_motor_status'] = original_read_status
        
    def inject_error_6_motor_fault_bit(self):
        """错误6: 电机故障状态(Bit3)"""
        print(f"[{get_timestamp()}] 注入错误6: 电机故障状态(Bit3)")
        
        original_read_status = Move_commands.read_motor_status
        
        def mock_read_status(ser, slave_addr, serial_lock):
            if self.error_injection_active and self.current_error_type == 6:
                # 返回故障状态：Bit3=1 (0x0008)
                return 0x0008  # 故障位设置
            return original_read_status(ser, slave_addr, serial_lock)
        
        Move_commands.read_motor_status = mock_read_status
        self.original_functions['read_motor_status'] = original_read_status
        
    def inject_error_7_timeout_fault(self):
        """错误7: 超时故障"""
        print(f"[{get_timestamp()}] 注入错误7: 超时故障")
        
        # 修改配置中的超时时间，使其很快超时
        original_timeout = None
        
        def modify_timeout_config(config):
            nonlocal original_timeout
            if self.error_injection_active and self.current_error_type == 7:
                for axis_key in ['motor_x', 'motor_y', 'motor_z', 'motor_cam']:
                    if axis_key in config:
                        if original_timeout is None:
                            original_timeout = config[axis_key].get('timeout', 10.0)
                        config[axis_key]['timeout'] = 0.1  # 设置很短的超时时间
            return config
        
        self.modify_config_func = modify_timeout_config
        
    def activate_error(self, error_type):
        """激活指定类型的错误"""
        self.error_injection_active = True
        self.current_error_type = error_type
        
        # 根据错误类型注入相应的错误
        if error_type == 1:
            self.inject_error_1_cumulative_reverse_movement()
        elif error_type == 2:
            self.inject_error_2_wrong_direction()
        elif error_type == 3:
            self.inject_error_3_exceed_tolerance_during_motion()
        elif error_type == 4:
            self.inject_error_4_delayed_sampling_deviation()
        elif error_type == 5:
            self.inject_error_5_status_read_failure()
        elif error_type == 6:
            self.inject_error_6_motor_fault_bit()
        elif error_type == 7:
            self.inject_error_7_timeout_fault()
            
    def deactivate_error(self):
        """停用错误注入"""
        self.error_injection_active = False
        self.current_error_type = None
        
        # 恢复原始函数
        for func_name, original_func in self.original_functions.items():
            setattr(Move_commands, func_name, original_func)
        self.original_functions.clear()
        
    def restore_all(self):
        """恢复所有修改"""
        self.deactivate_error()

class MockNetworkClient:
    """模拟网络客户端，用于接收硬件错误事件"""

    def __init__(self):
        self.received_events = []
        self.server_socket = None
        self.client_socket = None
        self.running = False

    def start_server(self, port=8888):
        """启动模拟服务器"""
        try:
            self.server_socket = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            self.server_socket.setsockopt(socket.SOL_SOCKET, socket.SO_REUSEADDR, 1)
            self.server_socket.bind(('localhost', port))
            self.server_socket.listen(1)
            self.running = True
            print(f"[{get_timestamp()}] 模拟网络客户端服务器启动，监听端口 {port}")
            return True
        except Exception as e:
            print(f"[{get_timestamp()}] 启动模拟服务器失败: {e}")
            return False

    def accept_connection(self):
        """接受连接"""
        try:
            self.client_socket, addr = self.server_socket.accept()
            print(f"[{get_timestamp()}] 接受连接来自: {addr}")
            return True
        except Exception as e:
            print(f"[{get_timestamp()}] 接受连接失败: {e}")
            return False

    def receive_events(self, timeout=10):
        """接收事件消息"""
        if not self.client_socket:
            return []

        self.client_socket.settimeout(timeout)
        events = []

        try:
            while self.running:
                data = self.client_socket.recv(4096).decode('utf-8')
                if not data:
                    break

                # 处理可能的多个JSON消息
                messages = data.strip().split('\n')
                for msg in messages:
                    if not msg.strip():
                        continue
                    try:
                        event = json.loads(msg)
                        events.append(event)
                        self.received_events.append(event)
                        print(f"[{get_timestamp()}] 接收到事件: {event.get('event_name', 'unknown')}")
                    except json.JSONDecodeError as e:
                        print(f"[{get_timestamp()}] JSON解析失败: {e}")

        except socket.timeout:
            print(f"[{get_timestamp()}] 接收事件超时")
        except Exception as e:
            print(f"[{get_timestamp()}] 接收事件异常: {e}")

        return events

    def stop(self):
        """停止服务器"""
        self.running = False
        if self.client_socket:
            self.client_socket.close()
        if self.server_socket:
            self.server_socket.close()

class HardwareErrorTester:
    """硬件错误测试器"""

    def __init__(self):
        self.simulator = HardwareErrorSimulator()
        self.network_client = MockNetworkClient()
        self.test_results = {}

    def setup_test_environment(self):
        """设置测试环境"""
        print(f"[{get_timestamp()}] 设置测试环境...")

        # 设置模拟串口
        self.simulator.setup_mock_serial()

        # 启动模拟网络客户端
        if not self.network_client.start_server():
            return False

        return True

    def test_single_error(self, error_type, error_name):
        """测试单个错误类型"""
        print(f"\n{'='*60}")
        print(f"[{get_timestamp()}] 开始测试错误类型 {error_type}: {error_name}")
        print(f"{'='*60}")

        test_result = {
            'error_type': error_type,
            'error_name': error_name,
            'error_detected': False,
            'emergency_stop_sent': False,
            'network_event_received': False,
            'test_start_time': time.time(),
            'test_duration': 0,
            'details': {}
        }

        try:
            # 激活错误注入
            self.simulator.activate_error(error_type)

            # 加载配置
            config = load_motor_config()
            if not config:
                test_result['details']['error'] = '配置加载失败'
                return test_result

            # 修改配置以适应测试
            config = self.modify_config_for_test(config, error_type)

            # 直接测试监控函数而不是完整的运动控制
            test_result = self.test_monitor_function_directly(error_type, error_name, config, test_result)

        except Exception as e:
            test_result['details']['exception'] = str(e)
            print(f"[{get_timestamp()}] ✗ 测试过程中发生异常: {e}")
            import traceback
            traceback.print_exc()

        finally:
            # 停用错误注入
            self.simulator.deactivate_error()
            test_result['test_duration'] = time.time() - test_result['test_start_time']

        return test_result

    def test_monitor_function_directly(self, error_type, error_name, config, test_result):
        """直接测试监控函数"""
        print(f"[{get_timestamp()}] 直接测试监控函数...")

        # 创建测试所需的对象
        completion_event = threading.Event()
        serial_lock = threading.Lock()
        global_stop_event = threading.Event()
        axis_fault_info = {}
        axis_fault_info_lock = threading.Lock()

        # 模拟初始脉冲和目标距离
        initial_pulse = 0
        target_distance_mm = 10.0  # 10mm正向移动

        try:
            # 启动监控线程
            monitor_thread = threading.Thread(
                target=Move_commands.monitor_axis_status_with_distance,
                args=(
                    self.simulator.mock_serial,
                    'motor_x',
                    config,
                    completion_event,
                    serial_lock,
                    global_stop_event,
                    get_timestamp,
                    None,  # do1_slave_addr
                    None,  # relay_control_func
                    initial_pulse,
                    target_distance_mm,
                    axis_fault_info,
                    axis_fault_info_lock
                )
            )

            monitor_thread.daemon = True
            monitor_thread.start()

            # 等待监控完成或超时
            monitor_thread.join(timeout=5.0)

            # 检查是否检测到错误
            with axis_fault_info_lock:
                if axis_fault_info:
                    test_result['error_detected'] = True
                    test_result['emergency_stop_sent'] = True  # 监控函数会设置全局停止事件
                    test_result['details']['faults'] = [
                        {"axis": k, "fault_msg": v.get("fault_msg", "")}
                        for k, v in axis_fault_info.items()
                    ]
                    print(f"[{get_timestamp()}] ✓ 错误检测成功，故障信息: {test_result['details']['faults']}")
                else:
                    print(f"[{get_timestamp()}] ✗ 未检测到预期的硬件错误")

            # 检查全局停止事件是否被设置
            if global_stop_event.is_set():
                test_result['emergency_stop_sent'] = True
                print(f"[{get_timestamp()}] ✓ 全局停止事件已设置")

        except Exception as e:
            test_result['details']['monitor_exception'] = str(e)
            print(f"[{get_timestamp()}] 监控函数测试异常: {e}")

        return test_result

    def modify_config_for_test(self, config, error_type):
        """根据错误类型修改配置"""
        test_config = copy.deepcopy(config)

        # 修改监控容差，使错误更容易触发
        if 'global_settings' not in test_config:
            test_config['global_settings'] = {}

        test_config['global_settings']['Monitor_tolerance'] = 1.0  # 1mm容差
        test_config['global_settings']['Monitor_status_delay'] = 0.1  # 快速监控

        # 为超时测试修改超时时间
        if error_type == 7:
            for axis_key in ['motor_x', 'motor_y', 'motor_z', 'motor_cam']:
                if axis_key in test_config:
                    test_config[axis_key]['timeout'] = 0.5  # 0.5秒超时

        return test_config

    def run_all_tests(self):
        """运行所有错误类型的测试"""
        error_types = [
            (1, "累计反向位移超限"),
            (2, "运动方向错误"),
            (3, "运动中超出容差"),
            (4, "延迟采样偏差超限"),
            (5, "读取状态失败"),
            (6, "电机故障状态(Bit3)"),
            (7, "超时故障")
        ]

        print(f"[{get_timestamp()}] 开始硬件错误模拟测试")
        print(f"总共测试 {len(error_types)} 种错误类型")

        for error_type, error_name in error_types:
            test_result = self.test_single_error(error_type, error_name)
            self.test_results[error_type] = test_result

            # 测试间隔
            time.sleep(1)

        return self.test_results

    def generate_test_report(self):
        """生成测试报告"""
        print(f"\n{'='*80}")
        print(f"硬件错误模拟测试报告")
        print(f"测试时间: {datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        print(f"{'='*80}")

        total_tests = len(self.test_results)
        passed_tests = 0

        for error_type, result in self.test_results.items():
            status = "✓ 通过" if result['error_detected'] else "✗ 失败"
            if result['error_detected']:
                passed_tests += 1

            print(f"\n错误类型 {error_type}: {result['error_name']}")
            print(f"  状态: {status}")
            print(f"  错误检测: {'是' if result['error_detected'] else '否'}")
            print(f"  急停发送: {'是' if result['emergency_stop_sent'] else '否'}")
            print(f"  网络事件: {'是' if result['network_event_received'] else '否'}")
            print(f"  测试耗时: {result['test_duration']:.2f}秒")

            if result['details']:
                print(f"  详细信息: {result['details']}")

        print(f"\n{'='*80}")
        print(f"测试总结:")
        print(f"  总测试数: {total_tests}")
        print(f"  通过数: {passed_tests}")
        print(f"  失败数: {total_tests - passed_tests}")
        print(f"  通过率: {passed_tests/total_tests*100:.1f}%")
        print(f"{'='*80}")

        return self.test_results

    def cleanup(self):
        """清理测试环境"""
        print(f"[{get_timestamp()}] 清理测试环境...")
        self.simulator.restore_all()
        self.network_client.stop()

def main():
    """主函数"""
    tester = HardwareErrorTester()

    try:
        # 设置测试环境
        if not tester.setup_test_environment():
            print("测试环境设置失败")
            return

        # 运行所有测试
        results = tester.run_all_tests()

        # 生成测试报告
        tester.generate_test_report()

    except KeyboardInterrupt:
        print(f"\n[{get_timestamp()}] 用户中断测试")
    except Exception as e:
        print(f"[{get_timestamp()}] 测试过程中发生错误: {e}")
        import traceback
        traceback.print_exc()
    finally:
        # 清理
        tester.cleanup()

if __name__ == "__main__":
    main()
