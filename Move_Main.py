# 单元测试程序：先回零所有轴，然后通过键盘输入目标物体编号进行抓取释放
# 同时作为游戏主程序的服务端，接受网络指令
import sys
import signal
import time
import json
import threading
import queue
import traceback
import datetime

from Move_Homing import home_axis
from Move_Control import run_motor_commands
from Move_Utils import (
    open_serial, close_serial, load_motor_config,
    serial_lock as global_utils_serial_lock,
    calculate_motion_times,
    smart_update_dynamic_config,
    update_config_mtime_cache
)
from Move_Coordinate import (
    get_tank_dimensions,
    validate_and_adjust_coordinates,
    calculate_motor_deltas
)
# 从 Move_Hardware 导入新的爪子状态检查函数
from Move_Hardware import check_gripper_status

from Move_ReleasePos import find_optimal_release_position

# 导入网络通信模块
from move_network import GameServiceServer, select_target_object, start_detection_client
import move_network # import latest_detection_fps, latest_detection_fps_lock  # 新增导入

# 配置文件路径常量
CONFIG_MOVE = "Config_Move.yaml"

# 保存当前各轴的位置
current_positions = {
    'X': 0.0,
    'Y': 0.0,
    'Z': 0.0,
    'CAM': 0.0
}

# 保存轴的偏移值
axis_offsets = {
    'X': 0.0,
    'Y': 0.0
}

# 全局电机配置，在main和command_processor中初始化
global_motor_config = None

# 新增：配置更新锁
config_lock = threading.Lock()

# 检测结果存储
latest_objects = []
objects_lock = threading.Lock()

# 指令队列
command_queue = queue.Queue()

# 串口对象（全局管理）
global_serial = None
# 使用从 Move_Utils 导入的全局锁，确保唯一性
serial_lock = global_utils_serial_lock

# 全局鱼缸尺寸，在main函数中初始化
tank_dimensions_global = None

# --- New Global Event for Program-Wide Stop ---
global_program_stop_event = threading.Event()

# --- Shared dictionary to track completion of ongoing axis movements by any command ---
# Key: motor_key (e.g., 'motor_x'), Value: threading.Event() that signals completion of that axis's current task.
active_axis_completion_events = {}
active_axis_completion_events_lock = threading.Lock() # Lock for accessing the above dictionary

def get_timestamp_main():
    """获取当前时间戳字符串，格式为HH:MM:SS.mmm"""
    now = datetime.datetime.now()
    return now.strftime("%H:%M:%S.") + f"{now.microsecond//1000:03d}"

def reload_and_update_config():
    """
    安全地重新加载并更新全局配置和相关派生变量。
    使用智能更新机制：只有在配置文件修改时间变化时才执行更新。
    """
    global global_motor_config, axis_offsets, tank_dimensions_global

    with config_lock:
        print(f"\n[{get_timestamp_main()}] ===== 游戏周期结束，检查配置更新 =====")

        # 使用智能更新函数，只有在配置文件修改时才执行更新
        if global_motor_config is not None:
            _, was_updated = smart_update_dynamic_config(global_motor_config, CONFIG_MOVE)

            if was_updated:
                print(f"[{get_timestamp_main()}] 配置已更新，刷新相关的全局变量...")

                # 根据更新后的配置，刷新相关的全局变量
                # 更新轴偏移 - 始终更新，不再比较新旧值
                new_offset_x = global_motor_config.get('motor_x', {}).get('offset', 0.0)
                new_offset_y = global_motor_config.get('motor_y', {}).get('offset', 0.0)
                print(f"[{get_timestamp_main()}] 轴偏移已更新: X={new_offset_x}, Y={new_offset_y}")
                axis_offsets['X'] = new_offset_x
                axis_offsets['Y'] = new_offset_y

                # 更新鱼缸尺寸 - 使用配置对象而不是文件路径
                new_tank_dims = get_tank_dimensions(global_motor_config) # 传递配置对象
                if new_tank_dims and (tank_dimensions_global is None or tank_dimensions_global != new_tank_dims):
                     print(f"[{get_timestamp_main()}] 鱼缸尺寸已更新: {new_tank_dims}")
                     tank_dimensions_global = new_tank_dims

                print(f"[{get_timestamp_main()}] 配置更新完成")
            else:
                print(f"[{get_timestamp_main()}] 配置文件未修改，无需更新")
        else:
            print(f"[{get_timestamp_main()}] 警告: 全局配置为None，跳过更新。")

        print(f"[{get_timestamp_main()}] ===== 配置检查流程结束 =====\n")

def get_detected_objects():
    """获取当前检测到的物体信息"""
    with objects_lock:
        return latest_objects.copy()

def wait_axes_ready(axis_keys, timeout=180):
    """等待指定轴的运动和监控线程全部完成"""
    start_time = time.monotonic()
    with active_axis_completion_events_lock:
        events = [active_axis_completion_events.get(axis) for axis in axis_keys if axis in active_axis_completion_events]
    for axis, event in zip(axis_keys, events):
        if event and not event.is_set():
            print(f"等待 {axis} 上一次运动和监控完全结束...")
            while not event.is_set():
                if time.monotonic() - start_time > timeout:
                    print(f"等待 {axis} 超时，放弃等待。")
                    break
                time.sleep(0.05)

def command_processor():
    """指令处理工作线程"""
    global global_serial, latest_objects, global_program_stop_event, global_motor_config

    print("指令处理线程已启动")

    # 指令处理器现在直接使用在 main 中加载和更新的全局配置
    if not global_motor_config:
        print("指令处理线程启动失败：全局电机配置未加载。线程将退出。")
        return

    while not global_program_stop_event.is_set():
        command_item = None
        try:
            command_item = command_queue.get(timeout=0.1)  # 缩短超时以便更频繁检查全局停止事件
            
            if global_program_stop_event.is_set():
                if command_item:  # 如果已经获取到指令但程序需要停止，放回队列
                    command_queue.put(command_item)
                break
                
            if command_item is None:  # 收到 None 信号，退出循环
                print("指令处理线程收到退出信号")
                break
                
            command_type = command_item.get('type')
            request_id = command_item.get('request_id', 'unknown')
            client_socket_for_event = command_item.get('client_socket')

            if command_type == 'movement':
                params = command_item.get('params', {})
                print(f"处理移动指令 {request_id}: {params}")
                
                # 从 params 中提取参数，如果不存在则使用当前位置或默认值
                target_x = params.get('target_x', current_positions['X'] + axis_offsets['X'])
                target_y = params.get('target_y', current_positions['Y'] + axis_offsets['Y'])
                target_z = params.get('target_z', current_positions['Z'])
                target_cam = params.get('target_cam', current_positions['CAM'])
                do2_state = params.get('do2_state', 2)  # 2 表示不操作
                
                # 使用全局配置对象
                if global_motor_config:
                    # 使用新的 process_movement 接口
                    success = process_movement(
                        target_x=target_x,
                        target_y=target_y,
                        target_z=target_z,
                        target_cam=target_cam,
                        do2_val=do2_state,
                        config_object=global_motor_config, # <--- 传递全局配置
                        current_main_lock=serial_lock,
                        current_serial_port=global_serial
                    )

                    if success:
                        print(f"移动指令 {request_id} 执行成功")
                    else:
                        print(f"移动指令 {request_id} 执行失败")
                else:
                    print(f"移动指令 {request_id} 执行失败：电机配置未加载")

            elif command_type == 'pick_object':
                params = command_item.get('params', {})
                object_id = params.get('object_id')
                client_socket_for_event = command_item.get('client_socket')
                request_id = command_item.get('request_id', 'unknown')

                with objects_lock:
                    current_detected_objects = list(latest_objects)

                final_object_id = select_target_object(
                    requested_id=object_id,
                    objects=current_detected_objects,
                    event_socket=client_socket_for_event,
                    request_id=request_id
                )

                if final_object_id is not None:
                    # command_processor 作为高层协调者，传递 serial_lock 和已加载的 global_motor_config
                    move_to_object(
                        object_number=final_object_id,
                        objects=current_detected_objects,
                        config=global_motor_config, # 传递全局配置
                        event_sender_socket=client_socket_for_event,
                        request_id=request_id,
                        serial_lock_obj_main=serial_lock, # 传递锁对象
                        command_params=params # 新增：传递完整的指令参数
                    )

            command_queue.task_done()  # 标记任务完成
            
        except queue.Empty:
            continue  # 超时则继续等待
        except Exception as e:
            print(f"处理指令时发生严重错误: {e}")
            print(traceback.format_exc())
            
            # 如果错误非常严重，考虑设置全局停止事件
            if isinstance(e, (IOError, OSError)):  # 例如：串口错误
                print("严重IO错误，设置全局停止事件")
                global_program_stop_event.set()
            
            # 尝试通知客户端错误
            active_command_item = locals().get('command_item') # 安全获取 command_item
            if active_command_item:
                error_client_socket = active_command_item.get('client_socket')
                error_request_id = active_command_item.get('request_id', 'unknown_on_error')
                error_object_id_params = active_command_item.get('params', {})
                error_object_id = error_object_id_params.get('object_id') if isinstance(error_object_id_params, dict) else None

                if error_client_socket:
                    try:
                        err_event_data = { # 修正变量名
                            'type': 'event',
                            'request_id': error_request_id,
                            'event_name': 'operation_error',
                            'data': {
                                'object_id': error_object_id,
                                'error_message': f'指令处理线程发生意外错误: {str(e)}',
                                'details': traceback.format_exc(),
                                'stage': 'command_processor_exception'
                            }
                        }
                        error_client_socket.sendall((json.dumps(err_event_data) + '\n').encode('utf-8')) # 修正变量名
                    except Exception as send_e:
                        print(f"发送指令处理线程错误事件失败: {send_e}")
            # 即使发生错误，也标记任务完成，防止队列阻塞
            if command_item:
                command_queue.task_done()

    print("指令处理线程结束")



def signal_handler(sig, frame):
    """处理Ctrl+C信号，优雅退出程序"""
    _ = sig, frame  # 忽略未使用的参数
    print("\n\n程序被用户中断，正在设置全局停止事件...")
    global_program_stop_event.set()  # 设置全局停止信号
    
    # 停止指令处理线程 - 使用None确保队列中的等待也能被中断
    try:
        command_queue.put_nowait(None)
    except queue.Full:
        pass
    
    # 不立即退出，让主循环处理清理工作
    print("请等待程序完成清理工作...")

def validate_position(axis_name, target_position, config):
    """验证目标位置是否有效"""
    motor_key = f"motor_{axis_name.lower()}"
    max_distance = config[motor_key].get('max_distance', float('inf'))

    if target_position < 0:
        return False, f"{axis_name}轴位置不能为负数"

    if target_position > max_distance:
        return False, f"{axis_name}轴位置超出最大限制 {max_distance}mm"

    return True, ""

def print_position_info():
    """打印当前位置信息"""
    print("\n===== 当前各轴位置 =====")
    print(f"X轴: {current_positions['X']:.3f} mm (含偏移: {current_positions['X'] + axis_offsets['X']:.3f} mm)")
    print(f"Y轴: {current_positions['Y']:.3f} mm (含偏移: {current_positions['Y'] + axis_offsets['Y']:.3f} mm)")
    print(f"Z轴: {current_positions['Z']:.3f} mm")
    print(f"CAM轴: {current_positions['CAM']:.3f} mm")
    print("========================")

def estimate_movement_time_to_object(target_x, target_y, target_z, config):
    """估算移动到目标物体的时间（静默计算，不打印详细信息）"""
    try:
        # 计算各轴移动距离
        current_world_x = current_positions['X'] + axis_offsets.get('X', 0.0)
        current_world_y = current_positions['Y'] + axis_offsets.get('Y', 0.0)

        distance_x = abs(target_x - current_world_x)
        distance_y = abs(target_y - current_world_y)
        distance_z = abs(target_z - current_positions['Z'])

        axis_distances = {
            'motor_x': distance_x,
            'motor_y': distance_y,
            'motor_z': distance_z,
            'motor_cam': 0.0
        }

        # 临时禁用详细打印，只计算时间
        import io
        import sys
        old_stdout = sys.stdout
        sys.stdout = io.StringIO()  # 重定向输出到内存

        try:
            _, _, sync_time, _ = calculate_motion_times(axis_distances, config)
            return sync_time
        finally:
            sys.stdout = old_stdout  # 恢复输出

    except Exception:
        return None

# 新实现的 process_movement 函数，替代之前的字符串解析版本
def process_movement(target_x=None, target_y=None, target_z=None, target_cam=None,
                     do2_val=2, current_main_lock=None, current_serial_port=None,
                     config_object=None): # 移除 config_path, 使用 config_object
    global current_positions, axis_offsets, global_serial, serial_lock, global_program_stop_event
    global active_axis_completion_events, active_axis_completion_events_lock

    timestamp = get_timestamp_main()
    if global_program_stop_event.is_set():
        print(f"[{timestamp}] 全局停止事件已设置，取消移动操作。")
        return False

    # 使用传入的配置对象，如果未提供则从文件加载
    config_to_use = config_object
    if config_to_use is None:
        # 如果没有提供配置对象，这是一个警告情况，但我们仍然可以尝试从默认文件加载
        print(f"[{timestamp}] 警告: process_movement 未收到 config_object，将从 {CONFIG_MOVE} 加载。")
        config_to_use = load_motor_config(CONFIG_MOVE)
        if not config_to_use:
            print(f"[{timestamp}] 无法加载移动配置 (路径: {CONFIG_MOVE})，取消移动操作。")
            return False
    
    # 从使用的配置中加载轴偏移
    axis_offsets_from_config = { # 使用新变量名以示区分
        'X': config_to_use.get('motor_x', {}).get('offset', 0.0),
        'Y': config_to_use.get('motor_y', {}).get('offset', 0.0)
    }

    # 使用新的坐标验证和调整函数
    adjusted_x, adjusted_y, adjusted_z, adjusted_cam, is_valid, error_message = validate_and_adjust_coordinates(
        target_x, target_y, target_z, target_cam,
        current_positions, axis_offsets_from_config, config_to_use, tank_dimensions_global
    )

    if not is_valid:
        print(f"[{timestamp}] 坐标验证失败: {error_message}")
        return False

    # 使用新的电机增量计算函数
    motor_deltas = calculate_motor_deltas(
        adjusted_x, adjusted_y, adjusted_z, adjusted_cam,
        current_positions, axis_offsets_from_config
    )

    delta_x = motor_deltas['motor_x']
    delta_y = motor_deltas['motor_y']
    delta_z = motor_deltas['motor_z']
    delta_cam = motor_deltas['motor_cam']
    eff_target_x_abs = motor_deltas['actual_motor_target_x']
    eff_target_y_abs = motor_deltas['actual_motor_target_y']
    eff_target_z_abs = adjusted_z
    eff_target_cam_abs = adjusted_cam
    
    # 记录请求的世界坐标和计算的电机增量
    print(f"\n[{timestamp}] ===== 准备移动 =====")
    print(f"[{timestamp}] 请求目标位置: " + 
          f"X={target_x if target_x is not None else '不变'}, " +
          f"Y={target_y if target_y is not None else '不变'}, " +
          f"Z={target_z if target_z is not None else '不变'}, " +
          f"CAM={target_cam if target_cam is not None else '不变'}")
    print(f"[{timestamp}] 调整后物理目标: X={adjusted_x if adjusted_x is not None else '不变'}, Y={adjusted_y if adjusted_y is not None else '不变'}") # 新增日志
    print(f"[{timestamp}] 当前电机位置: X={current_positions['X']:.3f}, Y={current_positions['Y']:.3f}, Z={current_positions['Z']:.3f}, CAM={current_positions['CAM']:.3f}")
    print(f"[{timestamp}] 使用的轴偏移值 (来自配置): X={axis_offsets_from_config['X']:.3f}, Y={axis_offsets_from_config['Y']:.3f}") # 明确来源
    print(f"[{timestamp}] 计算电机目标: X={eff_target_x_abs:.3f}, Y={eff_target_y_abs:.3f}, Z={eff_target_z_abs:.3f}, CAM={eff_target_cam_abs:.3f}")
    print(f"[{timestamp}] 计算移动距离: dX={delta_x:+.3f}, dY={delta_y:+.3f}, dZ={delta_z:+.3f}, dCAM={delta_cam:+.3f}")
    
    do2_map = {0: "抓取(0)", 1: "释放(1)", 2: "不变(2)"}
    print(f"[{timestamp}] DO2状态: {do2_val} ({do2_map.get(do2_val, '未知')})")
    print(f"[{timestamp}] =====================")

    # 使用提供的串口和锁，或回退到全局值
    ser_to_use = current_serial_port if current_serial_port else global_serial
    lock_to_use = current_main_lock if current_main_lock else serial_lock

    # 调用带有全局事件和轴完成字典的运动函数
    success = run_motor_commands(
        delta_x, delta_y, delta_z, delta_cam, do2_val,
        config_object=config_to_use,
        external_ser=ser_to_use,
        serial_lock_obj=lock_to_use,
        global_stop_event=global_program_stop_event,
        active_axis_events_dict=active_axis_completion_events,
        active_axis_events_lock=active_axis_completion_events_lock
    )

    # 新增：硬件错误统一处理
    if isinstance(success, dict) and not success.get('success', True):
        if success.get('error_type') == 'hardware_error':
            # 直接返回硬件错误详情
            return success
        else:
            # 其他错误类型可按需处理
            return False

    if success:
        # 修复：只更新实际移动的轴的位置
        # 这样可以避免并发线程覆盖其他线程已更新的轴位置
        if target_x is not None:
            current_positions['X'] = eff_target_x_abs
        if target_y is not None:
            current_positions['Y'] = eff_target_y_abs
        if target_z is not None:
            current_positions['Z'] = eff_target_z_abs
        if target_cam is not None:
            current_positions['CAM'] = eff_target_cam_abs
            
        # 使用当前时间戳记录操作完成
        print(f"[{get_timestamp_main()}] 移动操作成功完成! 新电机位置: " + 
              f"X={current_positions['X']:.3f}" + (f" (已更新)" if target_x is not None else "") + ", " +
              f"Y={current_positions['Y']:.3f}" + (f" (已更新)" if target_y is not None else "") + ", " +
              f"Z={current_positions['Z']:.3f}" + (f" (已更新)" if target_z is not None else "") + ", " +
              f"CAM={current_positions['CAM']:.3f}" + (f" (已更新)" if target_cam is not None else ""))
    else:
        # 使用当前时间戳记录操作失败
        print(f"[{get_timestamp_main()}] 移动操作失败或被中止!")
    return success

def display_detected_objects(objects, config=None):
    """显示检测到的物体信息"""
    # 新增：读取最近一次FPS
    fps_str = ""
    try:
        with move_network.latest_detection_fps_lock:
            # print(f"[DEBUG] 读取到FPS: {move_network.latest_detection_fps}")  # 检测FPS读取正常
            if move_network.latest_detection_fps is not None:
                fps_str = f"（当前FPS：{move_network.latest_detection_fps:.2f}）"
    except Exception:
        fps_str = ""
    if not objects:
        print(f"\n当前未检测到任何物体。{fps_str}")
        return

    print(f"\n当前检测到 {len(objects)} 个物体{fps_str}:")
    print("--------------------------------------------------------------------------------------")
    print("编号  |  类别ID      |  物理坐标(mm)   | 归一化比例 | 置信度 | 估算时间(秒)")
    print("--------------------------------------------------------------------------------------")
    
    # 修正：按编号(track_number)升序排序
    sorted_objects = sorted(
        objects,
        key=lambda obj: (obj.get("track_number") if isinstance(obj.get("track_number"), int) else float('inf'))
    )
    
    for obj in sorted_objects:
        track_number = obj.get("track_number", "N/A")
        class_id = obj.get("class_id", "N/A")
        class_name = obj.get("class_name", "Unknown")
        physical_coords = obj.get("physical_coords")
        norm_coords = obj.get("center_normalized_prop")
        confidence = obj.get("confidence", 0.0)

        phys_str = f"({physical_coords[0]:.1f}, {physical_coords[1]:.1f})" if physical_coords else "N/A"
        norm_str = f"({norm_coords[0]:.3f}, {norm_coords[1]:.3f})" if norm_coords else "N/A"
        
        # 估算移动时间
        time_est = "N/A"
        if physical_coords and config:
            est = estimate_movement_time_to_object(physical_coords[0], physical_coords[1], None, config)
            if est is not None:
                time_est = f"{est:.2f}"

        print(f"{track_number:<5} | {class_id:<2} ({class_name:<6}) | {phys_str:<15} | {norm_str:<18} | {confidence:.2f}   | {time_est}")

    print("--------------------------------------------------------------------------------------")
    print("输入物体编号移动到该位置，输入'r'刷新物体列表，输入'q'退出")

def prepare_objects_for_release_pos(objects):
    """准备物体列表，用于计算最佳投放位置"""
    prepared_objects = []

    for obj in objects:
        # 跳过当前被抓起的物体
        if obj.get("is_being_picked", False):
            continue

        # 获取已转换的物理坐标
        physical_coords = obj.get("physical_coords")
        if physical_coords and len(physical_coords) == 2:
            physical_x, physical_y = physical_coords
            prepared_objects.append({
                'physical_coords': (physical_x, physical_y),
                'class_name': obj.get("class_name"),
                'track_number': obj.get("track_number")
            })
        else:
            print(f"警告: 物体 {obj.get('track_number', 'N/A')} 缺少有效的 physical_coords，无法用于计算投放位置")

    return prepared_objects

def move_to_object(object_number, objects, config, event_sender_socket=None, request_id="unknown", serial_lock_obj_main=None, command_params=None):
    """移动到指定编号的物体位置并抓取，并通过 event_sender_socket 发送事件"""
    global global_serial, serial_lock, global_program_stop_event, current_positions, axis_offsets 
    
    
    # config 参数现在是由调用者（如 command_processor 或 main）传入的已加载配置
    base_log_prefix = f"ReqID:{request_id} 物体编号:{object_number} - " 

    def send_event(event_name, data):
        if event_sender_socket:
            try:
                event_message = {
                    'type': 'event',
                    'request_id': request_id,
                    'event_name': event_name,
                    'data': {**data, 'object_id': object_number}
                }
                event_sender_socket.sendall((json.dumps(event_message) + '\n').encode('utf-8'))
                print(f"[Event Sent] ReqID: {request_id}, Event: {event_name}, Data: {data}")
            except Exception as e:
                print(f"[ERROR] Failed to send event {event_name} for ReqID {request_id}: {e}")

    def send_error_and_complete(error_message, stage, status='error'):
        send_event('operation_error', {'error_message': error_message, 'stage': stage})
        send_event('cycle_completed', {
            'status': status,
            'message': f'抓取流程因错误终止: {error_message}',
            'final_positions': {
                'X': current_positions['X'] + axis_offsets.get('X', 0.0), 
                'Y': current_positions['Y'] + axis_offsets.get('Y', 0.0),
                'Z': current_positions['Z'],
                'CAM': current_positions['CAM']
            }
        })

    if global_program_stop_event.is_set():
        print(f"[{get_timestamp_main()}] {base_log_prefix}全局停止事件已设置，中止抓取流程")
        send_error_and_complete('全局停止事件已设置', 'global_stop_before_start') 
        return False

    if not global_serial or not global_serial.is_open:
        print(f"[{get_timestamp_main()}] {base_log_prefix}错误: 全局串口不可用，中止抓取流程")
        send_error_and_complete('全局串口不可用', 'serial_check_move_to_object')
        if not global_program_stop_event.is_set():
            global_program_stop_event.set()
        return False
    
    # 使用传入的锁，如果为None，则使用全局的 serial_lock
    current_serial_lock_for_this_op = serial_lock_obj_main if serial_lock_obj_main else serial_lock

    try:
        target_object = None
        
        for obj in objects:
            if obj.get("track_number") == object_number:
                target_object = obj
                break
        
        if not target_object:
            print(f"{base_log_prefix}未找到编号为 {object_number} 的物体")
            send_error_and_complete(f'未找到物体编号 {object_number}', 'object_search')
            return False

        physical_coords = target_object.get("physical_coords")
        if not physical_coords or len(physical_coords) != 2:
            print(f"{base_log_prefix}错误: 物体 {object_number} 缺少有效的 'physical_coords'")
            send_error_and_complete(f'物体 {object_number} 缺少物理坐标', 'object_data_check')
            return False
            
        physical_x, physical_y = physical_coords
        class_name = target_object.get("class_name", "N/A")
        class_id = str(target_object.get("class_id", "unknown"))

        # 缓存目标物体的名称，用于后续事件消息
        cached_object_name = class_name

        print(f"\n{base_log_prefix}准备移动到物体 #{object_number} ({class_name})")
        print(f"{base_log_prefix}物理坐标: ({physical_x:.1f}, {physical_y:.1f}) mm")
        print(f"\n{base_log_prefix}===== 开始抓取流程 =====")

        current_step = 0

        def log_step(description):
            nonlocal current_step
            current_step += 1
            print(f"{base_log_prefix}步骤 {current_step}: {description}")

        log_step(f"打开爪子(DO2=1)")
        move_result = process_movement(do2_val=1, config_object=config, current_main_lock=current_serial_lock_for_this_op, current_serial_port=global_serial)
        if isinstance(move_result, dict) and move_result.get('error_type') == 'hardware_error':
            send_event('hardware_error', {
                'error_message': '检测到电机硬件故障，已终止本轮操作',
                'faults': move_result.get('faults', [])
            })
            return False
        if not move_result:
            send_error_and_complete('打开爪子失败', 'opening_gripper')
            return False
        if global_program_stop_event.is_set(): send_error_and_complete('抓取中止于打开爪子后', 'aborted_after_gripper_open'); return False
        time.sleep(0.2) # 爪子动作延时



        log_step(f"移动X、Y轴到目标位置 ({physical_x:.1f}, {physical_y:.1f})")
        # 在每次运动前等待相关轴完成
        # 打开爪子(DO2=1)前无需等待
        # 移动X、Y轴到目标位置前
        wait_axes_ready(['motor_x', 'motor_y'])
        move_result = process_movement(target_x=physical_x, target_y=physical_y, config_object=config,
                                   current_main_lock=current_serial_lock_for_this_op, current_serial_port=global_serial)
        if isinstance(move_result, dict) and move_result.get('error_type') == 'hardware_error':
            send_event('hardware_error', {
                'error_message': '检测到电机硬件故障，已终止本轮操作',
                'faults': move_result.get('faults', [])
            })
            return False
        if not move_result:
            send_error_and_complete('移动到物体位置失败', 'moving_to_object_xy')
            return False
        if global_program_stop_event.is_set(): send_error_and_complete('抓取中止于XY移动后', 'aborted_after_xy_move'); return False
        
        # 从配置文件获取基础Z轴偏移
        z_offset_base = config.get('catch_settings', {}).get('class_z_offsets', {}).get(class_id, 0.0)
        
        # 从指令参数获取额外偏移，如果不存在或无效则默认为0
        z_offset_extra = 0.0
        if command_params:
            try:
                # 确保 z_offset_extra 是一个数字
                z_offset_extra = float(command_params.get('z_offset_extra', 0.0))
            except (ValueError, TypeError):
                z_offset_extra = 0.0
                print(f"{base_log_prefix}警告: 'z_offset_extra' 参数值无效，已忽略。")

        # 计算最终的Z轴偏移
        final_z_offset = z_offset_base + z_offset_extra
        
        target_z = config["motor_z"]["max_distance"] - final_z_offset

        log_step(f"Z轴下降到目标位置 ({target_z:.1f}mm)。类别'{class_name}'(ID:{class_id}) | 基础偏移: {z_offset_base:.1f}mm | 额外偏移: {z_offset_extra:.1f}mm | 总偏移: {final_z_offset:.1f}mm")
        # Z轴下降到目标位置前
        wait_axes_ready(['motor_z'])
        move_result = process_movement(target_z=target_z, config_object=config, current_main_lock=current_serial_lock_for_this_op, current_serial_port=global_serial)
        if isinstance(move_result, dict) and move_result.get('error_type') == 'hardware_error':
            send_event('hardware_error', {
                'error_message': '检测到电机硬件故障，已终止本轮操作',
                'faults': move_result.get('faults', [])
            })
            return False
        if not move_result:
            send_error_and_complete('Z轴下降失败', 'descending_z_axis')
            return False
        if global_program_stop_event.is_set(): send_error_and_complete('抓取中止于Z下降后', 'aborted_after_z_down'); return False
        
        log_step(f"闭合爪子抓取(DO2=0)")
        move_result = process_movement(do2_val=0, config_object=config, current_main_lock=current_serial_lock_for_this_op, current_serial_port=global_serial)
        if isinstance(move_result, dict) and move_result.get('error_type') == 'hardware_error':
            send_event('hardware_error', {
                'error_message': '检测到电机硬件故障，已终止本轮操作',
                'faults': move_result.get('faults', [])
            })
            return False
        if not move_result:
            send_error_and_complete('闭合爪子失败', 'closing_gripper')
            return False
        if global_program_stop_event.is_set(): send_error_and_complete('抓取中止于闭合爪子后', 'aborted_after_gripper_close'); return False
        
        catch_time = config.get('catch_settings', {}).get('catch_time', 0.5)
        log_step(f"等待抓取稳定时间 {catch_time}秒")
        end_time = time.time() + catch_time
        while time.time() < end_time:
            if global_program_stop_event.is_set(): send_error_and_complete('抓取中止于等待抓取稳定期间', 'aborted_during_catch_wait'); return False
            time.sleep(min(0.1, end_time - time.time()))

        temp_height_z = config['temp_height']['temp_height_z']
        log_step(f"Z轴上升到临时高度 ({temp_height_z:.1f}mm)")
        # Z轴上升到临时高度前
        wait_axes_ready(['motor_z'])
        move_result = process_movement(target_z=temp_height_z, config_object=config, current_main_lock=current_serial_lock_for_this_op, current_serial_port=global_serial)
        if isinstance(move_result, dict) and move_result.get('error_type') == 'hardware_error':
            send_event('hardware_error', {
                'error_message': '检测到电机硬件故障，已终止本轮操作',
                'faults': move_result.get('faults', [])
            })
            return False
        if not move_result:
            send_error_and_complete('Z轴上升到临时高度失败', 'ascending_to_hover_temp')
            return False
        if global_program_stop_event.is_set(): send_error_and_complete('抓取中止于Z到临时高度后', 'aborted_after_z_temp_up'); return False
        
        send_event('arm_at_hover_position', {
            'message': '机械臂已到达抓取后悬停判断位置',
            'current_positions': {
                'X': current_positions['X'] + axis_offsets.get('X', 0.0), 
                'Y': current_positions['Y'] + axis_offsets.get('Y', 0.0),
                'Z': current_positions['Z'],
                'CAM': current_positions['CAM']
            }
        })
        
        log_step(f"检查爪子状态，判断是否抓取到物体")
        # 直接调用爪子状态检查函数，避免嵌套锁
        has_object = check_gripper_status(
            ser=global_serial,
            config=config,
            serial_lock_obj=current_serial_lock_for_this_op
        )

        # 立即发送object_picked事件
        if has_object:
            # 成功抓取时只使用缓存的物体名称
            success_message = cached_object_name
        else:
            # 失败时使用通用消息
            success_message = '抓取失败，未检测到物体'

        send_event('object_picked', {
            'success': has_object,
            'object_id': object_number,
            'message': success_message
        })

        if not has_object:
            print(f"{base_log_prefix}未检测到物体，可能抓取失败")

            log_step(f"打开爪子(DO2=1) - 抓取失败路径")
            move_result = process_movement(do2_val=1, config_object=config, current_main_lock=current_serial_lock_for_this_op, current_serial_port=global_serial)
            if isinstance(move_result, dict) and move_result.get('error_type') == 'hardware_error':
                send_event('hardware_error', {
                    'error_message': '检测到电机硬件故障，已终止本轮操作',
                    'faults': move_result.get('faults', [])
                })
                return False
            if not move_result:
                send_event('operation_error', {'error_message': '抓取失败后打开爪子也失败', 'stage': 'open_gripper_after_fail_attempt'})
            if global_program_stop_event.is_set(): send_error_and_complete('抓取中止于打开爪子(失败路径)后', 'aborted_after_gripper_open_fail_path'); return False
            target_object["has_failed_attempt"] = True

            display_x = config['display_pos']['dis_pos_x']
            display_y = config['display_pos']['dis_pos_y']
            display_z = config['display_pos']['dis_pos_z']

            log_step(f"移动到展示位置 ({display_x:.1f}, {display_y:.1f}, {display_z:.1f}) - 抓取失败路径")
            move_result = process_movement(target_x=display_x, target_y=display_y, target_z=display_z, config_object=config,
                                   current_main_lock=current_serial_lock_for_this_op, current_serial_port=global_serial)
            if isinstance(move_result, dict) and move_result.get('error_type') == 'hardware_error':
                send_event('hardware_error', {
                    'error_message': '检测到电机硬件故障，已终止本轮操作',
                    'faults': move_result.get('faults', [])
                })
                return False
            if not move_result:
                send_error_and_complete('移动到展示位置失败（抓取失败后）', 'moving_to_display_after_pickup_failed', 'pickup_failed_with_move_error')
                return False
            if global_program_stop_event.is_set(): send_error_and_complete('抓取中止于移动到展示(失败路径)后', 'aborted_move_display_fail_path'); return False

            send_event('arm_at_display_position', {'has_object': False, 'message': '机械臂已到达展示位置 (抓取失败)'})

            display_time = config['display_pos']['dis_pos_time']
            log_step(f"展示位置停留 {display_time}秒 - 抓取失败路径")
            end_time_disp_fail = time.time() + display_time
            while time.time() < end_time_disp_fail:
                if global_program_stop_event.is_set(): send_error_and_complete('抓取中止于展示停留期间(失败路径)', 'aborted_during_display_wait_fail_path'); return False
                time.sleep(min(0.1, end_time_disp_fail - time.time()))

            log_step(f"回到初始位置 - 抓取失败路径")
            move_result = process_movement(target_x=axis_offsets.get('X', 0.0), target_y=axis_offsets.get('Y', 0.0), target_z=0.0, config_object=config,
                                   current_main_lock=current_serial_lock_for_this_op, current_serial_port=global_serial)
            if isinstance(move_result, dict) and move_result.get('error_type') == 'hardware_error':
                send_event('hardware_error', {
                    'error_message': '检测到电机硬件故障，已终止本轮操作',
                    'faults': move_result.get('faults', [])
                })
                return False
            if not move_result:
                send_error_and_complete('回到初始位置失败（抓取失败后）', 'returning_to_home_after_pickup_failed', 'pickup_failed_with_return_error')
                return False
            if global_program_stop_event.is_set(): send_error_and_complete('抓取中止于回到初始位置(失败路径)后', 'aborted_return_home_fail_path'); return False

            send_event('cycle_completed', {'status': 'pickup_failed', 'message': '抓取失败，机械臂已返回起点'})
            print(f"{base_log_prefix}未抓取到物体，本轮操作结束")
            return False

        # ---- HAS OBJECT ----
        print(f"{base_log_prefix}成功抓取到物体!")

        target_object["is_being_picked"] = True

        # ---- NEW: START CAM SHOWCASE THREAD (CONCURRENT) ----
        if config.get('camera_showcase_settings'):
            log_step(f"启动CAM轴并发展示线程。")
            cam_thread_log_prefix = f"ReqID:{request_id} ObjID:{object_number} CAMThread - "

            # 移除深拷贝，直接传递当前的全局配置引用
            # config_for_cam_thread = copy.deepcopy(config)  <- REMOVE THIS

            cam_showcase_thread = threading.Thread(
                target=_cam_showcase_thread_target,
                # 传递当前的全局配置对象，而不是它的拷贝
                args=(config, current_serial_lock_for_this_op, global_serial, global_program_stop_event, cam_thread_log_prefix),
                daemon=True, # Daemon thread will exit when main program exits
                name=f"CAMShowcase_{request_id}_{object_number}"
            )
            cam_showcase_thread.start()
            # DO NOT JOIN cam_showcase_thread HERE. Main flow continues.
        else:
            log_step(f"配置中未启用CAM轴展示。")
        # ---- END OF STARTING CAM SHOWCASE THREAD ----
        
        # Main XYZ flow continues immediately...
        temp_height_time = config['temp_height']['temp_height_time']
        log_step(f"XYZ流程: 临时高度停留 {temp_height_time}秒 (抓取成功后)") # Clarified this is XYZ flow
        end_time_temp_wait = time.time() + temp_height_time
        while time.time() < end_time_temp_wait:
            if global_program_stop_event.is_set(): send_error_and_complete('抓取中止于临时高度停留期间', 'aborted_during_temp_height_wait'); return False
            time.sleep(min(0.1, end_time_temp_wait - time.time()))
        
        display_x = config['display_pos']['dis_pos_x']
        display_y = config['display_pos']['dis_pos_y']
        display_z = config['display_pos']['dis_pos_z']
        
        log_step(f"XYZ流程: 移动到展示位置 ({display_x:.1f}, {display_y:.1f}, {display_z:.1f}) (抓取成功后)")
        # 移动到展示位置前
        wait_axes_ready(['motor_x', 'motor_y', 'motor_z'])
        move_result = process_movement(target_x=display_x, target_y=display_y, target_z=display_z, config_object=config,
                                   current_main_lock=current_serial_lock_for_this_op, current_serial_port=global_serial)
        if isinstance(move_result, dict) and move_result.get('error_type') == 'hardware_error':
            send_event('hardware_error', {
                'error_message': '检测到电机硬件故障，已终止本轮操作',
                'faults': move_result.get('faults', [])
            })
            return False
        if not move_result:
            send_error_and_complete('移动到展示位置失败（抓取成功后）', 'moving_to_display_after_pickup_success', 'pickup_success_but_display_failed')
            return False
        if global_program_stop_event.is_set(): send_error_and_complete('抓取中止于移动到展示位置后', 'aborted_after_move_to_display'); return False
        
        send_event('arm_at_display_position', {'has_object': True, 'message': '机械臂已到达展示位置 (成功抓取)'})
        
        display_time_main = config['display_pos']['dis_pos_time']
        log_step(f"XYZ流程: 展示位置停留 {display_time_main}秒 (抓取成功后)")
        end_time_disp_main = time.time() + display_time_main
        while time.time() < end_time_disp_main:
            if global_program_stop_event.is_set(): send_error_and_complete('抓取中止于展示停留期间', 'aborted_during_display_wait'); return False
            time.sleep(min(0.1, end_time_disp_main - time.time()))
        
        log_step(f"XYZ流程: 获取当前物体位置，计算最佳投放点")
        current_objects_for_release = get_detected_objects() # This should be thread-safe if latest_objects is locked
        prepared_objects_for_release = prepare_objects_for_release_pos(current_objects_for_release)
        
        if global_program_stop_event.is_set(): send_error_and_complete('抓取中止于计算投放点前', 'aborted_before_calc_release_pos'); return False
            
        release_x, release_y = find_optimal_release_position(prepared_objects_for_release, config) # Pass full config
        release_height_z = config['release_settings']['release_height_z']
        
        log_step(f"XYZ流程: 移动到投放位置 ({release_x:.1f}, {release_y:.1f}, {release_height_z:.1f})")
        # 移动到投放位置前
        wait_axes_ready(['motor_x', 'motor_y', 'motor_z'])
        move_result = process_movement(target_x=release_x, target_y=release_y, target_z=release_height_z, config_object=config,
                                   current_main_lock=current_serial_lock_for_this_op, current_serial_port=global_serial)
        if isinstance(move_result, dict) and move_result.get('error_type') == 'hardware_error':
            send_event('hardware_error', {
                'error_message': '检测到电机硬件故障，已终止本轮操作',
                'faults': move_result.get('faults', [])
            })
            return False
        if not move_result:
            send_error_and_complete('移动到投放位置失败', 'moving_to_release', 'pickup_success_but_release_move_failed')
            return False
        
        log_step(f"XYZ流程: 打开爪子释放物体(DO2=1)")
        move_result = process_movement(do2_val=1, config_object=config, current_main_lock=current_serial_lock_for_this_op, current_serial_port=global_serial)
        if isinstance(move_result, dict) and move_result.get('error_type') == 'hardware_error':
            send_event('hardware_error', {
                'error_message': '检测到电机硬件故障，已终止本轮操作',
                'faults': move_result.get('faults', [])
            })
            return False
        if not move_result:
            send_error_and_complete('打开爪子释放失败', 'releasing_object', 'pickup_success_but_release_failed')
            return False
        
        release_time = config['release_settings']['release_time']
        log_step(f"XYZ流程: 等待物体释放稳定时间 {release_time}秒")
        end_time_release = time.time() + release_time
        while time.time() < end_time_release:
            if global_program_stop_event.is_set(): send_error_and_complete('抓取中止于物体释放稳定期间', 'aborted_during_release_wait'); return False
            time.sleep(min(0.1, end_time_release - time.time()))
        
        log_step(f"XYZ流程: 回到初始位置 (抓取成功后)")
        # 回到初始位置前
        wait_axes_ready(['motor_x', 'motor_y', 'motor_z'])
        move_result = process_movement(target_x=axis_offsets.get('X', 0.0), target_y=axis_offsets.get('Y', 0.0), target_z=0.0, config_object=config,
                                   current_main_lock=current_serial_lock_for_this_op, current_serial_port=global_serial)
        if isinstance(move_result, dict) and move_result.get('error_type') == 'hardware_error':
            send_event('hardware_error', {
                'error_message': '检测到电机硬件故障，已终止本轮操作',
                'faults': move_result.get('faults', [])
            })
            return False
        if not move_result:
            send_error_and_complete('返回初始位置失败（完成释放后）', 'returning_to_home_after_success', 'success_but_return_failed')
            return False
        if global_program_stop_event.is_set(): send_error_and_complete('抓取中止于回到初始位置后', 'aborted_after_return_home'); return False
        
        send_event('cycle_completed', {
            'status': 'success',
            'message': '成功抓取并投放，机械臂已返回起点',
            'final_positions': {
                'X': current_positions['X'] + axis_offsets.get('X', 0.0),
                'Y': current_positions['Y'] + axis_offsets.get('Y', 0.0),
                'Z': current_positions['Z'],
                'CAM': current_positions['CAM'] # CAM position might be 0 if showcase finished, or its current if ongoing
            }
        })
        
        if "is_being_picked" in target_object: del target_object["is_being_picked"]
        if "has_failed_attempt" in target_object: del target_object["has_failed_attempt"]
        
        print(f"{base_log_prefix}成功抓取并投放物体，主流程操作结束。CAM展示线程（如果启动）将独立运行。")
        print(f"{base_log_prefix}===== 抓取主流程完成 =====")
        return True
        
    except Exception as e_outer:
        error_msg = f"抓取流程中发生未捕获的外部错误: {str(e_outer)}"
        print(f"{base_log_prefix}{error_msg}")
        tb_str = traceback.format_exc()
        print(tb_str)
        send_event('operation_error', {'error_message': error_msg, 'details': tb_str, 'stage': 'move_to_object_outer_exception'})
        send_event('cycle_completed', {
            'status': 'error',
            'message': f'抓取流程中发生严重错误: {str(e_outer)}',
            'final_positions': {
                'X': current_positions['X'] + axis_offsets.get('X', 0.0),
                'Y': current_positions['Y'] + axis_offsets.get('Y', 0.0),
                'Z': current_positions['Z'],
                'CAM': current_positions['CAM']
            }
        })
        if not global_program_stop_event.is_set():
            print(f"{base_log_prefix}设置全局停止事件，因为抓取流程发生严重异常")
            global_program_stop_event.set()
        return False
    finally:
        # 这个块总是在函数返回前执行，是执行周期结束任务的理想位置
        print(f"[{get_timestamp_main()}] {base_log_prefix}抓取周期函数 move_to_object 执行完毕，将触发配置重载。")
        reload_and_update_config()

def _cam_showcase_thread_target(config_at_start, serial_lock_for_cam, serial_port_for_cam, stop_event, base_log_prefix_cam):
    """
    CAM轴展示的线程目标函数。
    此函数独立运行，与主移动流程并发。
    """
    # config_at_start 是线程启动时全局配置的一个引用
    try:
        timestamp_cam = get_timestamp_main()
        print(f"[{timestamp_cam}] {base_log_prefix_cam}CAM展示线程启动。")

        # 在线程开始时，一次性读取本次任务所需的所有"业务"参数并存为局部变量
        # 这样，即使全局配置中途变化，这个线程的"目标"和"行为"也是固定的。
        cam_showcase_settings = config_at_start.get('camera_showcase_settings')
        if not cam_showcase_settings:
            print(f"[{get_timestamp_main()}] {base_log_prefix_cam}配置中未找到 camera_showcase_settings，CAM展示线程退出。")
            return

        delay_before_cam_move = cam_showcase_settings.get('delay_before_move', 0.2)
        dwell_at_cam_showcase = cam_showcase_settings.get('dwell_time_at_showcase', 0.3)
        cam_max_dist = config_at_start.get('motor_cam', {}).get('max_distance', 0.0)

        # 1. 等待延时
        print(f"[{get_timestamp_main()}] {base_log_prefix_cam}CAM展示: 等待 {delay_before_cam_move} 秒。")
        end_time_cam_delay = time.time() + delay_before_cam_move
        while time.time() < end_time_cam_delay:
            if stop_event.is_set():
                print(f"[{get_timestamp_main()}] {base_log_prefix_cam}CAM展示: 全局停止事件在延时期间触发，线程中止。")
                return
            time.sleep(min(0.05, end_time_cam_delay - time.time()))
        
        if stop_event.is_set(): # Re-check
            print(f"[{get_timestamp_main()}] {base_log_prefix_cam}CAM展示: 全局停止事件在延时后触发，线程中止。")
            return

        # 2. CAM轴移动到展示位置
        print(f"[{get_timestamp_main()}] {base_log_prefix_cam}CAM展示: CAM轴移动到展示位置 ({cam_max_dist:.1f}mm)。")
        # 调用 process_movement 时，使用局部变量 cam_max_dist 作为目标
        # 但传递的 config_object 仍然是线程启动时的那份配置引用
        if not process_movement(target_cam=cam_max_dist,
                                config_object=config_at_start, # 传递启动时的配置
                                current_main_lock=serial_lock_for_cam,
                                current_serial_port=serial_port_for_cam):
            print(f"[{get_timestamp_main()}] {base_log_prefix_cam}CAM展示: CAM轴移动到展示位失败。线程中止。")
            return
        if stop_event.is_set():
            print(f"[{get_timestamp_main()}] {base_log_prefix_cam}CAM展示: 全局停止事件在CAM移至展示位后触发，线程中止。")
            return

        # 3. CAM轴在展示位停留
        print(f"[{get_timestamp_main()}] {base_log_prefix_cam}CAM展示: CAM轴在展示位停留 {dwell_at_cam_showcase} 秒。")
        end_time_cam_dwell = time.time() + dwell_at_cam_showcase
        while time.time() < end_time_cam_dwell:
            if stop_event.is_set():
                print(f"[{get_timestamp_main()}] {base_log_prefix_cam}CAM展示: 全局停止事件在停留期间触发，线程中止。")
                return
            time.sleep(min(0.05, end_time_cam_dwell - time.time()))
        
        if stop_event.is_set(): # Re-check
            print(f"[{get_timestamp_main()}] {base_log_prefix_cam}CAM展示: 全局停止事件在停留后触发，线程中止。")
            return
            
        # 4. CAM轴回到初始位置
        print(f"[{get_timestamp_main()}] {base_log_prefix_cam}CAM展示: CAM轴回到初始位置 (0.0mm)。")
        # 归位动作的目标是固定的0.0，执行时也使用启动时的配置
        if not process_movement(target_cam=0.0,
                                config_object=config_at_start, # 同样传递启动时的配置，保证一致性
                                current_main_lock=serial_lock_for_cam,
                                current_serial_port=serial_port_for_cam):
            print(f"[{get_timestamp_main()}] {base_log_prefix_cam}CAM展示: CAM轴回到初始位置失败。线程中止。")
            return
        if stop_event.is_set():
            print(f"[{get_timestamp_main()}] {base_log_prefix_cam}CAM展示: 全局停止事件在CAM归位后触发，线程中止。")
            return
            
        print(f"[{get_timestamp_main()}] {base_log_prefix_cam}CAM轴展示序列完成。线程结束。")

    except Exception as e_cam_thread:
        print(f"[{get_timestamp_main()}] {base_log_prefix_cam}CAM展示线程发生未处理异常: {e_cam_thread}")
        traceback.print_exc()
    finally:
        print(f"[{get_timestamp_main()}] {base_log_prefix_cam}CAM展示线程最终退出。")




def main():
    """主程序入口"""
    global axis_offsets, global_serial, tank_dimensions_global, global_program_stop_event
    global current_positions, active_axis_completion_events, active_axis_completion_events_lock
    global global_motor_config # 声明使用全局配置
    
    print("=====================================")
    print("YOLO智能识别与电机定位系统 (网络版)")
    print("=====================================")
    
    signal.signal(signal.SIGINT, signal_handler)
    
    # 在 try 块外部声明，以便 finally 中可以访问
    detection_client = None
    game_server = None
    command_thread = None
    server_thread = None

    try:
        # 确保全局停止事件是复位状态
        global_program_stop_event.clear()
        
        # 加载主配置一次
        global_motor_config = load_motor_config(CONFIG_MOVE)
        if not global_motor_config:
            print("加载电机配置文件失败，程序退出")
            return

        # 初始化配置文件修改时间缓存（程序启动时的第一次加载）
        update_config_mtime_cache(CONFIG_MOVE)
        print(f"[{get_timestamp_main()}] 已初始化配置文件修改时间缓存")

        axis_offsets['X'] = global_motor_config['motor_x'].get('offset', 0.0)
        axis_offsets['Y'] = global_motor_config['motor_y'].get('offset', 0.0)
        
        global_serial = open_serial(global_motor_config['serial'])
        if not global_serial:
            print("无法打开串口，程序退出")
            return
        
        # 初始化鱼缸尺寸 - 使用配置对象
        tank_dimensions_global = get_tank_dimensions(global_motor_config) # 传递配置对象
        if tank_dimensions_global is None:
            print("初始化鱼缸尺寸失败，请检查配置。程序退出")
            return
        print(f"鱼缸物理尺寸已加载: X={tank_dimensions_global[0]}mm, Y={tank_dimensions_global[1]}mm")

        detection_client = start_detection_client(tank_dimensions_global, global_motor_config, global_program_stop_event, objects_lock, latest_objects)
        if not detection_client:
            print("检测客户端启动被取消（程序停止）")
            return
        
        command_thread = threading.Thread(target=command_processor)
        command_thread.daemon = True
        command_thread.start()
        
        service_config = global_motor_config.get('motormove_service', {}) # <--- 修改: 使用 global_motor_config
        service_host = service_config.get('host', 'localhost')
        service_port = service_config.get('port', 5556)
        
        game_server = GameServiceServer(service_host, service_port)
        server_thread = threading.Thread(target=game_server.start, args=(global_program_stop_event, command_queue, global_serial, serial_lock))
        server_thread.daemon = True
        server_thread.start()
        
        print("\n步骤1: 所有轴回零")
        
        # 传递全局停止事件和已加载的配置对象
        homing_success = home_axis(
            axis=None, 
            ser=global_serial, 
            serial_lock_obj=serial_lock,
            global_stop_event=global_program_stop_event,
            config_object=global_motor_config
        )
        
        if not homing_success:
            print("回零操作失败，程序退出")
            raise RuntimeError("回零操作失败")
            
        print("\n所有轴已成功回零")
        print(f"注意: X轴和Y轴有初始偏移值，当前实际位置为 X: {axis_offsets['X']} mm, Y: {axis_offsets['Y']} mm")
        
        # 回零后，电机内部位置为0，当前位置也应为0
        current_positions['X'] = 0.0
        current_positions['Y'] = 0.0
        current_positions['Z'] = 0.0
        current_positions['CAM'] = 0.0
        
        print("\n步骤2: 网络服务已启动")
        if detection_client:
            print(f"- 检测结果接收: 连接到 {detection_client.host}:{detection_client.port}")
        print(f"- 游戏指令服务: 监听 {service_host}:{service_port}")
        print("\n步骤3: 目标识别与定位控制")
        print("系统将持续接收检测结果并支持通过编号选择移动目标")
        print("同时作为服务端接受游戏程序的网络指令")
        print("\n按Ctrl+C可退出程序")
        
        print_position_info()
        
        # 主循环
        while not global_program_stop_event.is_set():
            # 检查服务线程是否仍在运行
            if not command_thread.is_alive():
                print("错误: 指令处理线程已停止！程序将退出。")
                global_program_stop_event.set()
                break
                
            if not server_thread.is_alive():
                print("错误: 游戏服务线程已停止！程序将退出。")
                global_program_stop_event.set()
                break
                
            if detection_client and not detection_client.running:
                print("错误: 检测客户端已停止运行！程序将退出。")
                global_program_stop_event.set()
                break

            # 显示当前检测到的物体
            objects_main_loop = get_detected_objects()
            display_detected_objects(objects_main_loop, global_motor_config)
            
            try:
                # 使用超时 input 或尝试非阻塞方式，避免长时间阻塞主循环
                # 这里简化处理，使用常规 input，但注意这会阻塞主循环直到用户输入
                # 在生产环境中可能需要更复杂的非阻塞输入处理
                user_input = input("\n请输入物体编号或命令(r=刷新, q=退出): ")
                
                if global_program_stop_event.is_set():
                    print("处理输入前检测到全局停止事件，跳过输入处理")
                    break
                
                if user_input.strip().lower() in ['q', 'quit', 'exit']:
                    print("用户请求退出程序")
                    global_program_stop_event.set()
                    break
                    
                if user_input.strip().lower() == 'r':
                    print("刷新物体列表...")
                    continue
                  

                # 尝试解析为物体编号，进行抓取
                try:
                    object_number = int(user_input.strip())
                    
                    if global_program_stop_event.is_set():
                        break
                        
                    move_to_object(
                        object_number=object_number, 
                        objects=objects_main_loop,
                        config=global_motor_config, # 从主函数传递已加载的配置
                        serial_lock_obj_main=serial_lock
                    )
                except ValueError:
                    print("输入无效，请输入正确的物体编号、'r'刷新或'q'退出")
                
            except Exception as e_loop:
                print(f"处理主循环输入时出错: {e_loop}")
                print(traceback.format_exc())
                # 根据错误严重性决定是否设置全局停止事件
                if isinstance(e_loop, (IOError, OSError)):  # 系统级错误
                    print("遇到系统级错误，设置全局停止事件")
                    global_program_stop_event.set()
                    break
                continue
    # --- BEGINNING OF CRITICAL FIX ---
    except RuntimeError as rte:
        print(f"程序因运行时错误终止: {rte}")
        global_program_stop_event.set()
    except KeyboardInterrupt:
        print("\n程序被用户中断")
        global_program_stop_event.set()
    except Exception as e_main:
        print(f"程序主函数发生严重错误: {str(e_main)}")
        print(traceback.format_exc())
        global_program_stop_event.set()
    
    finally:
        print("\n程序正在退出，开始清理资源...")
        
        # 确保全局停止事件已设置
        if not global_program_stop_event.is_set():
            global_program_stop_event.set()
        
        if detection_client:
            print("正在停止检测客户端...")
            detection_client.stop()
        
        if game_server:
            print("正在停止游戏服务端...")
            game_server.stop()
        
        print("正在发送退出信号给指令处理线程...")
        try:
            command_queue.put_nowait(None)  # 非阻塞方式发送退出信号
        except queue.Full:
            print("警告: 指令队列已满，无法发送退出信号")
        
        if command_thread and command_thread.is_alive():
            print("等待指令处理线程退出...")
            command_thread.join(timeout=3.0)
            if command_thread.is_alive():
                print("警告: 指令处理线程未在3秒内退出")

        if server_thread and server_thread.is_alive():
            print("等待游戏服务线程退出...")
            server_thread.join(timeout=3.0)
            if server_thread.is_alive():
                print("警告: 游戏服务线程未在3秒内退出")

        if global_serial:
            print("关闭串口连接")
            close_serial(global_serial)
            global_serial = None
    
    print("\n程序已结束")
    # --- END OF CRITICAL FIX ---

if __name__ == "__main__":
    # 可能需要导入一些额外模块
    # import select  # 用于 GameServiceServer._handle_game_client 中的非阻塞socket操作
    try:
        main()
    except Exception as fatal_error:
        print(f"程序入口点发生致命错误: {fatal_error}")
        print(traceback.format_exc())
        sys.exit(1)
