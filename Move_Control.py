#可独立运行也可被调用，传入4轴移动距离毫米和DO2目标状态，返回成败
import time
import datetime
import argparse
import threading
from Move_Utils import open_serial, close_serial, calculate_motion_times, load_motor_config
from Move_Utils import serial_lock as global_serial_lock, trace_lock
from Move_commands import move_motor, move_motor_with_speed, monitor_axis_status_with_distance, read_motor_position # 新增 read_motor_position
from Move_Hardware import relay_control

def get_timestamp():
    """获取当前时间戳字符串，格式为HH:MM:SS.mmm"""
    now = datetime.datetime.now()
    return now.strftime("%H:%M:%S.") + f"{now.microsecond//1000:03d}"

def run_motor_commands(
    x_distance, y_distance, z_distance, cam_distance, do2_state,
    config_object, external_ser=None, serial_lock_obj=None,
    global_stop_event=None,
    active_axis_events_dict=None,
    active_axis_events_lock=None
):
    config = config_object # Use the passed config object
    if not config: return False

    # log_prefix is for the overall function invocation time.
    # For specific actions within the function, use get_timestamp() directly.
    initial_call_timestamp_str = get_timestamp()
    print(f"[{initial_call_timestamp_str}] run_motor_commands invoked.")

    if global_stop_event and global_stop_event.is_set():
        print(f"[{get_timestamp()}] Global stop event already set at function entry. Aborting.") # Current time
        return False

    using_external_ser = external_ser is not None
    ser_to_use = external_ser
    current_lock = serial_lock_obj if serial_lock_obj else global_serial_lock
    internal_ser_opened = False
    
    this_command_monitor_threads = []
    this_command_completion_events = {} 

    z_axis_completion_event_this_command = None 
    z_monitor_thread_this_command = None
    do1_was_opened_for_z_move = False

    axis_fault_info = {}
    axis_fault_info_lock = threading.Lock()

    try:
        if not using_external_ser:
            ser_to_use = open_serial(config['serial'])
            if not ser_to_use: 
                print(f"[{get_timestamp()}] Failed to open serial port.") # Current time
                return False
            internal_ser_opened = True
        
        global_max_speed = config.get('global_settings', {}).get('max_speed_rpm', float('inf'))
        # --- Configuration Logging ---
        current_ts_config = get_timestamp()
        print(f"\n[{current_ts_config}] ===== 全局速度设置 =====")
        print(f"[{current_ts_config}] 最大速度限制: {global_max_speed} RPM")
        print(f"[{current_ts_config}] ========================\n")
        
        print(f"[{current_ts_config}] ===== Z轴配置信息 =====")
        z_speed_cfg = config.get('motor_z', {}).get('speed', global_max_speed)
        z_speed = min(z_speed_cfg, global_max_speed)
        print(f"[{current_ts_config}] Z轴导程: {config.get('motor_z', {}).get('lead')} mm/转")
        print(f"[{current_ts_config}] Z轴设置速度: {z_speed_cfg} RPM")
        print(f"[{current_ts_config}] Z轴实际速度: {z_speed} RPM" + (" (已限速)" if z_speed < z_speed_cfg else ""))
        print(f"[{current_ts_config}] Z轴最大加速度: {config.get('motor_z', {}).get('max_acceleration')} m/s²")
        print(f"[{current_ts_config}] Z轴从站地址: {config.get('motor_z', {}).get('slave_addr')}")
        print(f"[{current_ts_config}] ========================")

        axis_distances = {
            'motor_x': x_distance, 'motor_y': y_distance,
            'motor_z': z_distance, 'motor_cam': cam_distance
        }
        axes_to_move_in_this_command = [k for k, v in axis_distances.items() if v != 0]

        # 用字典保存每个轴的目标距离
        target_distances = {
            'motor_x': x_distance,
            'motor_y': y_distance,
            'motor_z': z_distance,
            'motor_cam': cam_distance
        }

        # 1. 在所有运动命令下发前，读取各轴初始脉冲
        initial_pulses = {}
        for axis_key in axes_to_move_in_this_command:
            motor_cfg = config.get(axis_key, {})
            if not motor_cfg.get('supports_status_read', False):
                continue
            slave_addr = motor_cfg.get('slave_addr')
            pulse = read_motor_position(ser_to_use, slave_addr, current_lock)
            initial_pulses[axis_key] = pulse
            print(f"[{get_timestamp()}] {axis_key} 启动前脉冲数: {pulse}")

        # 运动时间估算和速度调整
        z_axis_time, max_time_est, sync_time_est, adjusted_speeds = calculate_motion_times(axis_distances, config)

        # 2. 创建监控线程时传递 initial_pulse 和目标距离
        if active_axis_events_dict is not None and active_axis_events_lock is not None:
            prior_events_to_wait_for = []
            with active_axis_events_lock:
                for axis_key in axes_to_move_in_this_command:
                    motor_cfg = config.get(axis_key, {})
                    if not motor_cfg.get('supports_status_read', False):
                        print(f"[{get_timestamp()}] {axis_key} does not support status read or not configured for it. Skipping monitor creation.") # Current time
                        continue

                    completion_event = threading.Event()
                    this_command_completion_events[axis_key] = completion_event
                    active_axis_events_dict[axis_key] = completion_event 

                    initial_pulse = initial_pulses.get(axis_key)
                    target_distance = target_distances.get(axis_key)
                    if axis_key == 'motor_z':
                        do1_slave_addr = config.get('do1', {}).get('slave_addr')
                        thread = threading.Thread(
                            target=monitor_axis_status_with_distance,
                            name=f"ZMonitor-{get_timestamp()}",
                            args=(ser_to_use, axis_key, config, completion_event,
                                current_lock, global_stop_event, get_timestamp,
                                do1_slave_addr, relay_control, initial_pulse, target_distance,
                                axis_fault_info, axis_fault_info_lock)  # 新增
                        )
                    else:
                        thread = threading.Thread(
                            target=monitor_axis_status_with_distance,
                            name=f"{axis_key.replace('motor_','').upper()}Monitor-{get_timestamp()}",
                            args=(ser_to_use, axis_key, config, completion_event,
                                current_lock, global_stop_event, get_timestamp,
                                None, None, initial_pulse, target_distance,
                                axis_fault_info, axis_fault_info_lock)  # 新增
                        )
                    thread.daemon = True
                    this_command_monitor_threads.append(thread)
        
        if do2_state != 2:
            print(f"[{get_timestamp()}] Controlling DO2 to: {'ON' if do2_state == 1 else 'OFF'}") # Current time
            relay_control(ser_to_use, 1, do2_state, config['do2']['slave_addr'], current_lock)
        if global_stop_event and global_stop_event.is_set(): return False

        if axis_distances.get('motor_cam', 0) != 0:
            cam_speed = adjusted_speeds.get('motor_cam', config.get('motor_cam',{}).get('speed'))
            print(f"[{get_timestamp()}] Sending CAM command: {axis_distances['motor_cam']:.3f} mm, Speed: {cam_speed:.1f} RPM") # Current time
            move_motor_with_speed(ser_to_use, axis_distances['motor_cam'], config, 'motor_cam', cam_speed, current_lock)
            # 仅对CAM轴加延迟，避免监控线程过早判定为STOPPED
            time.sleep(0.2)
        if global_stop_event and global_stop_event.is_set(): return False

        need_xyz_sync_move = (axis_distances.get('motor_x', 0) != 0 or 
                            axis_distances.get('motor_y', 0) != 0 or 
                            axis_distances.get('motor_z', 0) != 0)

        if need_xyz_sync_move:
            do1_slave_addr = config.get('do1', {}).get('slave_addr')
            if axis_distances.get('motor_z', 0) != 0 and do1_slave_addr:
                pre_z_delay = config.get('do1', {}).get('pre_z_delay', 0.05)
                if pre_z_delay > 0: # 仅当 pre_z_delay 大于0时才执行延时逻辑
                    print(f"[{get_timestamp()}] Pre-Z delay: {pre_z_delay}s, then opening DO1 brake.")
                    if global_stop_event:
                        # global_stop_event.wait 返回 True 如果事件被设置, False 如果超时
                        if global_stop_event.wait(timeout=pre_z_delay):
                            print(f"[{get_timestamp()}] Global stop detected during Pre-Z delay. Aborting.")
                            return False
                        # 如果 wait 超时, 说明延时已完成且事件未被设置
                    else:
                        # 如果没有 global_stop_event, 则执行普通的 sleep
                        time.sleep(pre_z_delay)
                
                # 在延时之后（或者如果没有延时）再次检查全局停止事件
                if global_stop_event and global_stop_event.is_set():
                    print(f"[{get_timestamp()}] Global stop detected before opening DO1 brake. Aborting.")
                    return False

                relay_control(ser_to_use, 0, 1, do1_slave_addr, current_lock) 
                do1_was_opened_for_z_move = True
            if global_stop_event and global_stop_event.is_set(): return False # 保留此检查

            if axis_distances.get('motor_x', 0) != 0:
                x_speed = adjusted_speeds.get('motor_x', config.get('motor_x',{}).get('speed'))
                print(f"[{get_timestamp()}] Sending X command: {axis_distances['motor_x']:.3f} mm, Speed: {x_speed:.1f} RPM") # Current time
                move_motor_with_speed(ser_to_use, axis_distances['motor_x'], config, 'motor_x', x_speed, current_lock)
            if global_stop_event and global_stop_event.is_set(): return False

            if axis_distances.get('motor_y', 0) != 0:
                y_speed = adjusted_speeds.get('motor_y', config.get('motor_y',{}).get('speed'))
                print(f"[{get_timestamp()}] Sending Y command: {axis_distances['motor_y']:.3f} mm, Speed: {y_speed:.1f} RPM") # Current time
                move_motor_with_speed(ser_to_use, axis_distances['motor_y'], config, 'motor_y', y_speed, current_lock)
            if global_stop_event and global_stop_event.is_set(): return False
            
            if axis_distances.get('motor_z', 0) != 0:
                z_speed_cmd = adjusted_speeds.get('motor_z', config.get('motor_z',{}).get('speed'))
                print(f"[{get_timestamp()}] Sending Z command: {axis_distances['motor_z']:.3f} mm, Speed: {z_speed_cmd:.1f} RPM") # Current time
                move_motor_with_speed(ser_to_use, axis_distances['motor_z'], config, 'motor_z', z_speed_cmd, current_lock)
            if global_stop_event and global_stop_event.is_set(): return False

        if z_monitor_thread_this_command: 
            this_command_monitor_threads.append(z_monitor_thread_this_command)
        
        if this_command_monitor_threads:
            print(f"[{get_timestamp()}] Starting {len(this_command_monitor_threads)} monitor thread(s) for this command...") # Current time
            for thread_idx, thread in enumerate(this_command_monitor_threads):
                thread.start()
                # Optional: Log each thread start if needed for very fine-grained debugging
                # print(f"[{get_timestamp()}] Monitor thread {thread.name} (idx {thread_idx}) started.")
            print(f"[{get_timestamp()}] All {len(this_command_monitor_threads)} monitor threads for this command have been started.") # Current time
        else:
            print(f"[{get_timestamp()}] No monitor threads to start for this command.") # Current time


        # --- Waiting for THIS command's axes ---
        if this_command_completion_events:
            print(f"[{get_timestamp()}] Now waiting for {len(this_command_completion_events)} monitored axis/axes of this command to complete...") # Current time
        else:
            print(f"[{get_timestamp()}] No axes being monitored for completion in this command.") # Current time
        
        all_completed_successfully = True
        
        motion_timeout_setting = config.get('global_settings', {}).get('motion_timeout', 180)

        for axis_key, event in this_command_completion_events.items():
            print(f"[{get_timestamp()}] Waiting for {axis_key} to complete...") # Current time
            start_wait_time = time.monotonic()
            
            # Calculate specific timeout for this axis based on its estimated move time
            # For simplicity, using a portion of sync_time_est or a fixed buffer
            # This is a fallback; the monitor thread should set the event promptly.
            # If sync_time_est is 0 (e.g. only CAM moved), use motion_timeout_setting
            axis_specific_timeout = (sync_time_est if sync_time_est > 0 else max_time_est) + 15 # 15s buffer
            if axis_specific_timeout <= 15 and len(axes_to_move_in_this_command) > 0 : # if only cam moved or very short move
                axis_specific_timeout = motion_timeout_setting


            while not event.is_set():
                if global_stop_event and global_stop_event.is_set():
                    print(f"[{get_timestamp()}] Global stop detected while waiting for {axis_key}.") # Use current timestamp
                    all_completed_successfully = False
                    break
                if time.monotonic() - start_wait_time > axis_specific_timeout :
                    print(f"[{get_timestamp()}] Timeout waiting for {axis_key} to complete (waited {time.monotonic() - start_wait_time:.2f}s, timeout {axis_specific_timeout:.2f}s). Signaling global stop.") # Use current timestamp
                    if global_stop_event: global_stop_event.set() 
                    all_completed_successfully = False
                    # Ensure the event is set so the loop doesn't hang if monitor failed to set it
                    if not event.is_set(): event.set() 
                    # 新增：超时也写入 axis_fault_info
                    with axis_fault_info_lock:
                        axis_fault_info[axis_key] = {
                            'fault_type': 'timeout_fault',
                            'fault_msg': f'等待轴{axis_key}超时'
                        }
                    break
                time.sleep(0.05) 
            
            if not all_completed_successfully: 
                break 
            print(f"[{get_timestamp()}] {axis_key} completed.") # Use current timestamp here

        # 检查故障上报
        with axis_fault_info_lock:
            if axis_fault_info:
                faults = [
                    {"axis": k, "fault_msg": v.get("fault_msg", "")}
                    for k, v in axis_fault_info.items()
                ]
                # ========== 新增：检测到硬件故障后，所有轴急停 ==========
                print(f"[{get_timestamp()}] 检测到硬件故障，立即向所有轴发送急停命令！")
                # 发送急停命令（Bit6=1）到所有支持的轴
                for axis_key in ['motor_x', 'motor_y', 'motor_z', 'motor_cam']:
                    motor_cfg = config.get(axis_key, {})
                    slave_addr = motor_cfg.get('slave_addr')
                    if not slave_addr:
                        continue
                    data_emg_stop = bytes([0x00, 0x4E, 0x00, 0x40])
                    current_lock = serial_lock_obj if serial_lock_obj else global_serial_lock
                    try:
                        with current_lock:
                            if ser_to_use:
                                # 正确的Modbus命令格式应包含CRC校验，这里建议直接调用send_modbus_command
                                from Move_commands import send_modbus_command
                                send_modbus_command(ser_to_use, slave_addr, 0x06, data_emg_stop)
                                time.sleep(0.05)
                    except Exception as e:
                        print(f"[{get_timestamp()}] 急停命令发送失败: {axis_key} - {e}")
                # Z轴抱闸
                do1_slave_addr = config.get('do1', {}).get('slave_addr')
                if do1_slave_addr:
                    try:
                        print(f"[{get_timestamp()}] 急停后Z轴抱闸（DO1）")
                        relay_control(ser_to_use, 0, 0, do1_slave_addr, current_lock)
                    except Exception as e:
                        print(f"[{get_timestamp()}] 急停后Z轴抱闸失败: {e}")
                # ========== 急停结束 ==========
                return {'success': False, 'error_type': 'hardware_error', 'faults': faults}

        if not all_completed_successfully:
            print(f"[{get_timestamp()}] Not all axes for this command completed successfully or global stop was triggered.") # Use current timestamp
            return False

        print(f"[{get_timestamp()}] All monitored axes for this command instance completed.") # Use current timestamp
        return True

    except Exception as e:
        print(f"[{get_timestamp()}] run_motor_commands (invoked at [{initial_call_timestamp_str}]) unhandled exception: {e}") # Shows both times
        import traceback
        traceback.print_exc()
        if global_stop_event: global_stop_event.set() 
        return False
    finally:
        for thread in this_command_monitor_threads:
            if thread.is_alive():
                thread.join(timeout=1.0)
                if thread.is_alive():
                    print(f"[{get_timestamp()}] Warning: Monitor thread {thread.name} did not exit cleanly.") # Use current timestamp
        
        if do1_was_opened_for_z_move and ser_to_use and config.get('do1', {}).get('slave_addr'):
            post_z_delay = config.get('do1', {}).get('post_z_delay', 0.01)
            # Check global stop before this sleep as well
            if not (global_stop_event and global_stop_event.is_set()):
                 print(f"[{get_timestamp()}] Post-Z delay: {post_z_delay}s, then closing DO1 brake.") # Use current timestamp
                 time.sleep(post_z_delay) # This sleep might be interrupted if global_stop_event is set elsewhere
            
            # Always try to close brake if it was opened, regardless of stop event, for safety
            try:
                print(f"[{get_timestamp()}] Ensuring DO1 brake is closed.") # Use current timestamp
                relay_control(ser_to_use, 0, 0, config['do1']['slave_addr'], current_lock) 
            except Exception as ed:
                print(f"[{get_timestamp()}] Error closing DO1 in finally: {ed}") # Use current timestamp
        
        if internal_ser_opened and ser_to_use:
            close_serial(ser_to_use)
        print(f"[{get_timestamp()}] run_motor_commands finished.") # Use current timestamp

if __name__ == "__main__":
    parser = argparse.ArgumentParser(description='控制四轴电机和DO状态')
    parser.add_argument('x_distance', type=float, help='X轴移动距离（毫米，正负表示方向）')
    parser.add_argument('y_distance', type=float, help='Y轴移动距离（毫米，正负表示方向）')
    parser.add_argument('z_distance', type=float, help='Z轴移动距离（毫米，正负表示方向）')
    parser.add_argument('cam_distance', type=float, help='CAM轴移动距离（毫米，正负表示方向）')
    parser.add_argument('do2_state', type=int, help='DO2状态 (0=关闭, 1=开启, 2=不操作)')
    args = parser.parse_args()

    # For standalone execution, create dummy global event and dicts
    dummy_global_stop = threading.Event()
    dummy_active_axis_events = {}
    dummy_active_axis_lock = threading.Lock()

    # For standalone execution, load config from file first
    config_for_standalone = load_motor_config("Config_Move.yaml")
    if not config_for_standalone:
        print("Failed to load motor config for standalone run. Exiting.")
        exit(1)

    result = run_motor_commands(
        args.x_distance, args.y_distance, args.z_distance, args.cam_distance, args.do2_state,
        config_object=config_for_standalone, # Pass the loaded config
        global_stop_event=dummy_global_stop,
        active_axis_events_dict=dummy_active_axis_events,
        active_axis_events_lock=dummy_active_axis_lock
    )

    if result:
        print("所有操作成功完成!")
        exit(0)
    else:
        print("操作过程中出现错误!")
        exit(1)