import time
import threading
import signal
from Move_Utils import load_motor_config
from Move_Control import run_motor_commands

def main():
    config_path = "Config_Move.yaml"
    config = load_motor_config(config_path)
    if not config:
        print("无法加载配置文件，退出。")
        return

    y_max_distance = config['motor_y']['max_distance']
    y_speed = config['motor_y']['speed']

    # 用于优雅退出
    stop_event = threading.Event()

    def signal_handler(sig, frame):
        print("\n检测到Ctrl-C，准备退出...")
        stop_event.set()

    signal.signal(signal.SIGINT, signal_handler)

    print("开始Y轴往返测试，按Ctrl-C退出。")
    pos = y_max_distance
    while not stop_event.is_set():
        print(f"\n>>> Y轴正向移动 {pos} mm")
        run_motor_commands(
            x_distance=0, y_distance=pos, z_distance=0, cam_distance=0, do2_state=2,
            config_object=config,
            global_stop_event=stop_event,
            active_axis_events_dict={},
            active_axis_events_lock=threading.Lock()
        )
        if stop_event.is_set():
            break
        time.sleep(1)

        print(f"\n>>> Y轴反向移动 {-pos} mm")
        run_motor_commands(
            x_distance=0, y_distance=-pos, z_distance=0, cam_distance=0, do2_state=2,
            config_object=config,
            global_stop_event=stop_event,
            active_axis_events_dict={},
            active_axis_events_lock=threading.Lock()
        )
        if stop_event.is_set():
            break
        time.sleep(1)

if __name__ == "__main__":
    main()