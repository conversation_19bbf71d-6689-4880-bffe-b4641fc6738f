#此程序独立运行时，键盘可输入要回零的轴，不输入则回零所有轴
#作为模块被调用时，可指定要回零的轴，不指定则回零所有轴，返回成败true/false
import time
import datetime
import argparse
from Move_Utils import open_serial, close_serial, crc16, load_motor_config
from Move_Utils import serial_lock as global_serial_lock, trace_lock
from Move_commands import send_modbus_command, read_motor_status # <--- MODIFIED: Import read_motor_status
from Move_Hardware import relay_control  # 使用合并后的硬件模块中的继电器控制函数
import threading # Ensure threading is imported if not already

def get_timestamp():
    """获取当前时间戳字符串，格式为HH:MM:SS.mmm"""
    now = datetime.datetime.now()
    return now.strftime("%H:%M:%S.") + f"{now.microsecond//1000:03d}"

# def read_motor_status(ser, slave_addr, serial_lock_obj=None): # <--- DELETED THIS FUNCTION DEFINITION
#     """
#     读取电机状态 (PA_004)
#     
#     Args:
#         ser: 串口对象
#         slave_addr: 从站地址
#         
#     Returns:
#         状态值，如果读取失败则返回None
#     """
#     read_data = bytes([
#         0x00, 0x04,  # 参数地址 (PA_004)
#         0x00, 0x01   # 寄存器数量
#     ])
#     
#     # 发送读取命令
#     read_request_core = bytes([slave_addr, 0x03]) + read_data
#     read_request = read_request_core + crc16(read_request_core)
#     
#     response_bytes = None
#     current_lock = serial_lock_obj if serial_lock_obj else global_serial_lock
# 
#     trace_lock(f"Attempting to acquire for status read (Addr:{slave_addr})", current_lock)
#     with current_lock:
#         trace_lock(f"Acquired for status read (Addr:{slave_addr})", current_lock)
#         try:
#             if ser.in_waiting > 0:
#                 ser.read(ser.in_waiting)
#             ser.write(read_request)
#             time.sleep(0.05)
#             if ser.in_waiting > 0:
#                 response_bytes = ser.read(ser.in_waiting)
#         finally:
#             trace_lock(f"Released for status read (Addr:{slave_addr})", current_lock)
#             
#     if not response_bytes:
#         return None
# 
#     # 解析响应数据
#     start_idx = -1
#     for i in range(len(response_bytes) - 4):
#         if response_bytes[i] == slave_addr and response_bytes[i+1] == 0x03:
#             start_idx = i
#             break
#     
#     if start_idx >= 0 and start_idx + 4 < len(response_bytes):
#         byte_count = response_bytes[start_idx + 2]
#         if start_idx + 3 + byte_count <= len(response_bytes):
#             # 正确解析响应中的状态值
#             status_value = (response_bytes[start_idx + 3] << 8) + response_bytes[start_idx + 4]
#             return status_value
#     return None

def set_homing_parameters(ser, slave_addr, homing_params, serial_lock_obj=None):
    """
    设置回零参数 - 使用单一命令发送所有参数
    
    Args:
        ser: 串口对象
        slave_addr: 从站地址
        homing_params: 回零参数字典
    """
    # 解析参数
    homing_mode = homing_params['mode']
    homing_speed = homing_params['speed']
    creep_speed = homing_params['creep_speed']
    accel_time = homing_params['accel_time']
    
    # 计算偏移值的高16位和低16位
    offset = homing_params['offset']
    offset_h = (offset >> 16) & 0xFFFF
    offset_l = offset & 0xFFFF
    
    print(f"\n===== 设置回零参数 =====")
    print(f"回零方式: {homing_mode}")
    print(f"回零速度: {homing_speed} RPM")
    print(f"回零爬行速度: {creep_speed} RPM")
    print(f"回零加减速时间: {accel_time} ms")
    print(f"原点偏移值: {offset}")
    print("========================")
    
    # 构建数据包，一次性发送所有回零参数
    data = bytes([
        0x00, 0x40,  # 起始地址 (PA_040)
        0x00, 0x06,  # 寄存器数量 (6个寄存器: PA_040到PA_045)
        0x0C,        # 字节数 (6个寄存器 * 2字节 = 12字节)
        # PA_040 (回零方式)
        0x00, homing_mode,
        # PA_041 (回零速度)
        0x00, homing_speed,
        # PA_042 (回零爬行速度)
        0x00, creep_speed,
        # PA_043 (回零加减速时间)
        (accel_time >> 8) & 0xFF, accel_time & 0xFF,
        # PA_044 (原点偏移值高16位)
        (offset_h >> 8) & 0xFF, offset_h & 0xFF,
        # PA_045 (原点偏移值低16位)
        (offset_l >> 8) & 0xFF, offset_l & 0xFF
    ])
    
    current_lock = serial_lock_obj if serial_lock_obj else global_serial_lock
    trace_lock(f"Attempting to acquire for homing params", current_lock)
    with current_lock:
        trace_lock(f"Acquired for homing params", current_lock)
        try:
            # 发送所有参数
            send_modbus_command(ser, slave_addr, 0x10, data)
        finally:
            trace_lock(f"Released for homing params", current_lock)

def trigger_homing(ser, slave_addr, serial_lock_obj=None):
    """
    触发回零操作 (PA_04E Bit4 = 1)
    
    Args:
        ser: 串口对象
        slave_addr: 从站地址
    """
    # 向PA_04E写入0x0010 (Bit4置1)，触发回零
    data = bytes([0x00, 0x4E, 0x00, 0x10])
    current_lock = serial_lock_obj if serial_lock_obj else global_serial_lock
    
    trace_lock(f"Attempting to acquire for homing trigger (Addr:{slave_addr})", current_lock)
    with current_lock:
        trace_lock(f"Acquired for homing trigger (Addr:{slave_addr})", current_lock)
        try:
            send_modbus_command(ser, slave_addr, 0x06, data)
        finally:
            trace_lock(f"Released for homing trigger (Addr:{slave_addr})", current_lock)

def check_homing_status(ser, slave_addr, serial_lock_obj, max_wait_time=180, global_stop_event=None): # Added global_stop_event
    """
    检查回零状态，等待回零完成
    
    Args:
        ser: 串口对象
        slave_addr: 从站地址
        max_wait_time: 最大等待时间(秒)
        global_stop_event: 全局停止事件
        
    Returns:
        True: 回零成功完成
        False: 回零失败或超时
    """
    print("\n开始监测回零状态...")
    start_time = time.time()
    check_interval = 0.05  # 每0.05秒检查一次 (was 0.5)
    last_status = None

    while time.time() - start_time < max_wait_time:
        if global_stop_event and global_stop_event.is_set():
            print(f"[{get_timestamp()}] Homing check for Addr:{slave_addr} aborted by global stop signal.")
            return False
            
        status = read_motor_status(ser, slave_addr, serial_lock_obj) # This will now call the imported version
        
        if status is not None and status != last_status:
            last_status = status
            status_desc = []
            if status & 0x0001: status_desc.append("到位")
            if status & 0x0002: status_desc.append("回零完成")
            if status & 0x0004: status_desc.append("电机运行中")
            if status & 0x0008: status_desc.append("故障")
            if status & 0x0010: status_desc.append("电机使能")
            if status & 0x0020: status_desc.append("正软限位")
            if status & 0x0040: status_desc.append("负软限位")
            print(f"[{get_timestamp()}] 状态更新: {', '.join(status_desc) if status_desc else '无特殊状态'}")

            if status & 0x0002:  # 回零完成
                elapsed = time.time() - start_time
                print(f"[{get_timestamp()}] 回零完成! 用时: {elapsed:.2f}秒")
                return True
            if status & 0x0008:  # 故障
                print(f"[{get_timestamp()}] 检测到电机故障，回零失败!")
                return False
        
        # 等待一段时间再检查
        # time.sleep(check_interval) # Replaced by wait on event or sleep
        if global_stop_event:
            if global_stop_event.wait(timeout=check_interval): # Wait with timeout
                print(f"[{get_timestamp()}] Homing check for Addr:{slave_addr} interrupted by global stop during wait.")
                return False # Event was set
        else:
            time.sleep(check_interval) # Fallback if no event provided
    
    print(f"[{get_timestamp()}] 回零操作超时，已等待{max_wait_time}秒!")
    return False

def run_axis_homing(axis_key='motor_z', motor_config_path="Config_Move.yaml", 
                   ser=None, serial_lock_obj=None, global_stop_event=None, config_object=None):
    """执行指定轴的回零操作
    
    Args:
        axis_key: 轴配置键名 (例如 'motor_z' 表示Z轴)
        motor_config_path: 配置文件路径
        ser: 外部传入的串口对象，如果为None则内部打开串口
        global_stop_event: 全局停止事件
        config_object: (可选) 外部传入的已加载配置对象
    
    Returns:
        True: 成功
        False: 失败
    """
    try:
        if global_stop_event and global_stop_event.is_set():
            print(f"Homing for {axis_key} aborted at start due to global stop signal.")
            return False

        # 使用传入的配置对象，如果未提供则从文件加载
        config = config_object if config_object else load_motor_config(motor_config_path)
        if not config:
            print("无法加载配置文件")
            return False
        
        # 检查是否有有效的回零配置
        if 'homing' not in config:
            print("配置文件中缺少回零参数配置")
            return False
        
        # 检查请求的轴是否存在
        if axis_key not in config:
            print(f"配置文件中不存在轴 '{axis_key}'")
            return False
            
        # 打印轴配置参数
        axis_name = axis_key.replace('motor_', '').upper()
        print(f"\n===== {axis_name}轴回零配置信息 =====")
        axis_config = config[axis_key]
        slave_addr = axis_config['slave_addr']
        print(f"{axis_name}轴从站地址: {slave_addr}")
        print(f"{axis_name}轴导程: {axis_config['lead']} mm/转")
        
        # 获取回零参数
        homing_params = config['homing']
        max_wait_time = homing_params.get('max_wait_time', 180)  # 默认180秒超时
        print(f"回零最大等待时间: {max_wait_time}秒")
        
        # 判断是否为Z轴，只有Z轴才需要控制继电器
        is_z_axis = (axis_key == 'motor_z')
        
        # 如果是Z轴，获取延迟时间配置
        if is_z_axis:
            pre_z_delay = config['do1'].get('pre_z_delay', 0.5)  # 默认500毫秒
            post_z_delay = config['do1'].get('post_z_delay', 0.01)  # 默认10毫秒
            print(f"DO1开启前延迟: {pre_z_delay}秒")
            print(f"DO1关闭后延迟: {post_z_delay}秒")
        print("================================")
        
        # 判断是否使用外部传入的串口对象
        using_external_ser = ser is not None
        
        # 如果没有外部串口，自行打开一个
        internal_ser_opened = False # Track if we opened it
        if not using_external_ser:
            ser = open_serial(config['serial'])
            if not ser:
                print("无法打开串口")
                return False
            internal_ser_opened = True
            
        try:
            if global_stop_event and global_stop_event.is_set():
                print(f"Homing for {axis_key} aborted before param set due to global stop.")
                return False
            print(f"\n===== {axis_name}轴回零流程 =====")
            current_lock = serial_lock_obj if serial_lock_obj else global_serial_lock
            # 设置回零参数
            set_homing_parameters(ser, slave_addr, homing_params, current_lock)
            
            # 只有Z轴才需要控制继电器
            if is_z_axis:
                # 打开继电器1(DO1) - 使用华控继电器控制
                print(f"\n[{get_timestamp()}] 打开DO1输出")
                relay_control(ser, 0, 1, config['do1']['slave_addr'], current_lock)  # 控制继电器1(DO1)打开
                
                # 等待指定的延迟时间
                print(f"[{get_timestamp()}] 等待pre_z_delay: {pre_z_delay:.3f}秒")
                time.sleep(pre_z_delay)
            
            # 触发回零操作
            print(f"[{get_timestamp()}] 开始{axis_name}轴回零操作")
            trigger_homing(ser, slave_addr, current_lock)
            
            # 监测回零状态
            homing_success = check_homing_status(ser, slave_addr, current_lock, max_wait_time, global_stop_event) # Pass event
            
            if global_stop_event and global_stop_event.is_set():
                print(f"Homing for {axis_key} aborted during status check by global stop.")
                # Attempt to ensure brake is applied if Z-axis
                if is_z_axis:
                    try:
                        print(f"[{get_timestamp()}] Global stop during Z-axis homing, ensuring DO1 is closed.")
                        relay_control(ser, 0, 0, config['do1']['slave_addr'], current_lock)
                    except Exception as e_brake:
                        print(f"Error ensuring DO1 closed on global stop during Z homing: {e_brake}")
                return False

            # 只有Z轴才需要控制继电器
            if is_z_axis:
                # 额外延迟后关闭DO1
                print(f"[{get_timestamp()}] {axis_name}轴回零{'成功' if homing_success else '失败'}，等待post_z_delay: {post_z_delay:.3f}秒")
                time.sleep(post_z_delay)
                
                # 关闭继电器1(DO1) - 使用华控继电器控制
                print(f"[{get_timestamp()}] 关闭DO1输出")
                relay_control(ser, 0, 0, config['do1']['slave_addr'], current_lock)  # 控制继电器1(DO1）关闭
            else:
                print(f"[{get_timestamp()}] {axis_name}轴回零{'成功' if homing_success else '失败'}")
            
            print("=======================")
            return homing_success
            
        finally:
            # 确保关闭DO1和串口，只有Z轴才需要确保关闭继电器
            try:
                if is_z_axis:
                    # 确保关闭继电器1(DO1)
                    relay_control(ser, 0, 0, config['do1']['slave_addr'], current_lock)
            except Exception as e:
                print(f"关闭DO1时发生异常: {str(e)}")
                
            # 只有在内部打开串口时才关闭
            if internal_ser_opened and ser: # Use the flag
                print("关闭串口连接")
                close_serial(ser)
    
    except Exception as e:
        print(f"操作失败: {str(e)}")
        import traceback
        print(traceback.format_exc())  # 打印完整的堆栈跟踪信息
        return False

def run_all_axis_homing(motor_config_path="Config_Move.yaml", ser=None, serial_lock_obj=None, global_stop_event=None, config_object=None):
    """
    按Z轴、X轴、Y轴、cam轴的顺序依次回零所有轴
    每个轴只有在前一个轴成功回零后才开始回零
    任何轴回零失败则立即退出整个流程
    
    Args:
        motor_config_path: 配置文件路径
        ser: 外部传入的串口对象，如果为None则内部打开串口
    
    Returns:
        True: 所有轴回零成功
        False: 至少一个轴回零失败
    """
    if global_stop_event and global_stop_event.is_set():
        print("All-axis homing aborted at start due to global stop signal.")
        return False
    # 按指定顺序排列轴
    axis_order = ['motor_z', 'motor_x', 'motor_y', 'motor_cam']
    
    print("\n===== 开始回零所有轴 =====")
    print(f"回零顺序: Z轴 -> X轴 -> Y轴 -> CAM轴")
    print("注意: 只有前一个轴回零成功后才会开始下一个轴的回零")
    
    # 使用传入的配置对象，如果未提供则从文件加载
    config = config_object if config_object else load_motor_config(motor_config_path)
    if not config:
        print("无法加载配置文件")
        return False
    
    # 检查每个轴是否在配置中存在
    available_axes = []
    for axis in axis_order:
        if axis in config:
            available_axes.append(axis)
        else:
            print(f"配置文件中不存在轴 '{axis}'，将跳过")
    
    if not available_axes:
        print("配置文件中没有找到有效的轴配置")
        return False
    
    # 判断是否使用外部传入的串口对象
    using_external_ser = ser is not None
    internal_ser_opened = False # Track if we opened it
    
    # 如果没有外部串口，自行打开一个
    if not using_external_ser:
        ser = open_serial(config['serial'])
        if not ser:
            print("无法打开串口")
            return False
        internal_ser_opened = True
    
    current_lock = serial_lock_obj if serial_lock_obj else global_serial_lock

    try:
        # 逐个执行轴回零，只有前一个轴回零成功后才会处理下一个轴
        for i, axis in enumerate(available_axes):
            if global_stop_event and global_stop_event.is_set():
                print(f"All-axis homing aborted before homing {axis} due to global stop signal.")
                return False
            axis_name = axis.replace('motor_', '').upper()
            print(f"\n开始 {axis_name}轴 回零... ({i+1}/{len(available_axes)})")
            # 将配置对象传递下去
            success = run_axis_homing(axis, motor_config_path, ser, current_lock, global_stop_event, config_object=config)
            
            if success:
                print(f"{axis_name}轴回零成功!")
            else:
                print(f"\n错误: {axis_name}轴回零失败! 终止后续轴的回零操作。")
                return False  # 任何轴回零失败立即退出并返回失败
        
        print("\n===== 所有轴回零操作完成 =====")
        print("结果: 全部成功")
        return True
        
    finally:
        # 只有在内部打开串口时才关闭
        if internal_ser_opened and ser: # Use the flag
            print("关闭串口连接")
            close_serial(ser)

# 为了向后兼容，保留原始函数名
def run_z_axis_homing(motor_config_path="Config_Move.yaml", global_stop_event=None): # Added global_stop_event
    """
    执行Z轴回零操作（向后兼容函数）
    
    Args:
        motor_config_path: 配置文件路径
        global_stop_event: 全局停止事件
    
    Returns:
        True: 成功
        False: 失败
    """
    return run_axis_homing('motor_z', motor_config_path, global_stop_event=global_stop_event) # Pass event

def home_axis(axis=None, config_path="Config_Move.yaml", ser=None, serial_lock_obj=None, global_stop_event=None, config_object=None):
    """
    回零指定轴或所有轴
    
    Args:
        axis: (可选) 要回零的轴的键名 (e.g., 'motor_x')。如果为None，则回零所有轴。
        config_path: 配置文件路径
        ser: (可选) 外部传入的串口对象
        serial_lock_obj: (可选) 外部传入的锁对象
        global_stop_event: (可选) 全局停止事件
        config_object: (可选) 外部传入的已加载配置对象
    
    Returns:
        True: 全部任务成功
        False: 任何任务失败
    """
    if global_stop_event and global_stop_event.is_set():
        print(f"Homing for {'axis ' + axis if axis else 'all axes'} aborted at start due to global stop signal.")
        return False

    current_lock = serial_lock_obj if serial_lock_obj else global_serial_lock
    if axis:
        # 回零单一轴
        print(f"开始回零 {axis.replace('motor_', '').upper()}轴...")
        return run_axis_homing(axis, config_path, ser, current_lock, global_stop_event, config_object=config_object) # Pass event
    else:
        # 回零所有轴
        print("开始回零所有轴...")
        return run_all_axis_homing(config_path, ser, current_lock, global_stop_event, config_object=config_object) # Pass event

if __name__ == "__main__":
    parser = argparse.ArgumentParser(description='电机回零操作')
    parser.add_argument('--axis', default=None, choices=['motor_x', 'motor_y', 'motor_z', 'motor_cam'], 
                        help='要回零的轴 (不指定则回零所有轴)')
    parser.add_argument('--config', default="Config_Move.yaml", help='电机配置文件路径')
    args = parser.parse_args()
    
    # 创建一个虚拟的全局停止事件，因为此脚本独立运行时没有外部事件
    stop_event_main = threading.Event()
    # 使用新的接口函数
    success = home_axis(args.axis, args.config, global_stop_event=stop_event_main) # Pass event
    
    if success:
        print("\n回零操作成功完成!")
