# 动态配置更新功能说明

## 功能概述

为 `move_main.py` 增加了动态配置更新功能，在每一轮游戏结束后（无论是独立运行游戏，还是从网络获取指令做游戏），自动重新读取 `config_move.yaml` 配置文件，并更新部分参数。这样可以在不重启整个程序的情况下动态调整电机的运行参数。

## 更新的参数

### 完全更新的配置部分
以下配置部分会被完全覆盖：
- `global_settings` - 全局设置
- `fish_tank` - 鱼缸尺寸
- `display_pos` - 展示位置
- `catch_settings` - 抓取动作设置
- `temp_height` - 临时判断高度
- `camera_showcase_settings` - 摄像头展示动作配置
- `release_settings` - 物体投放设置
- `homing` - 回零参数配置

### 部分更新的电机参数
对于四个轴（motor_x、motor_y、motor_z、motor_cam），只更新以下参数：
- `speed` - 运行速度
- `max_acceleration` - 最大加速度
- `max_distance` - 最大行程
- `offset` - 轴偏移（如果有）

### 不会更新的参数
以下参数不会被更新，保持程序运行时的稳定性：
- `serial` - 串口配置
- `motormove_service` - 网络服务配置
- `detection_service` - 检测服务配置
- `camera` - 摄像头配置
- 电机的 `slave_addr`、`pulse_per_rev`、`lead` 等硬件相关参数
- `do1`、`do2`、`X1` 等硬件接口配置

## 实现原理

### 核心函数

#### 1. `update_dynamic_config()` (Move_Utils.py)
```python
def update_dynamic_config(current_config, config_path="Config_Move.yaml"):
    """
    从配置文件重新加载并更新动态参数。
    此函数会原地修改传入的 current_config 字典，并保证操作的原子性。

    原子性保证：
    - 使用深拷贝创建配置副本进行修改
    - 只有在所有更新都成功后才应用到原始配置
    - 如果中途出错，原始配置保持不变
    """
```

#### 2. `reload_and_update_config()` (Move_Main.py)
```python
def reload_and_update_config():
    """
    安全地重新加载并更新全局配置和相关派生变量。
    """
```

### 触发时机

配置更新在 `move_to_object` 函数的 `finally` 块中触发，确保每次游戏周期结束后都会执行：

```python
def move_to_object(...):
    try:
        # 抓取流程代码
        ...
    except Exception as e:
        # 异常处理
        ...
    finally:
        # 无论成功还是失败，都会执行配置更新
        reload_and_update_config()
```

### 线程安全

#### 1. 配置更新锁
使用 `config_lock` 确保配置更新过程的线程安全：

```python
config_lock = threading.Lock()

def reload_and_update_config():
    with config_lock:
        # 配置更新代码
        ...
```

#### 2. CAM轴线程隔离
为了避免并发的CAM轴展示线程与主流程的配置更新产生竞态条件，CAM轴线程使用配置的深拷贝：

```python
# 传递配置的深拷贝给线程，避免竞态条件
config_for_cam_thread = copy.deepcopy(config)

cam_showcase_thread = threading.Thread(
    target=_cam_showcase_thread_target,
    args=(config_for_cam_thread, ...),  # 使用独立的配置副本
    daemon=True
)
```

## 使用方法

### 1. 运行时动态调整参数

1. 程序正常运行时，修改 `Config_Move.yaml` 文件中的相关参数
2. 执行一次抓取操作（通过键盘输入物体编号或网络指令）
3. 抓取操作完成后，新的配置参数会自动生效

### 2. 示例场景

**场景1：调整电机速度**
```yaml
# 修改 Config_Move.yaml 中的速度参数
motor_x:
  speed: 200  # 从150改为200

motor_y:
  speed: 220  # 从180改为220
```

**场景2：调整展示位置**
```yaml
# 修改展示位置
display_pos:
  dis_pos_x: 650.0  # 从600.0改为650.0
  dis_pos_y: 350.0  # 从342.0改为350.0
```

**场景3：调整抓取参数**
```yaml
# 修改抓取等待时间
catch_settings:
  catch_time: 0.8  # 从0.5改为0.8
```

## 日志输出

配置更新过程会产生详细的日志输出：

```
[12:34:56.789] ===== 游戏周期结束，开始更新配置 =====
[12:34:56.790] 正在从 Config_Move.yaml 重新加载动态配置...
  - 已更新部分: global_settings
  - 已更新部分: display_pos
  - 正在更新 motor_x 的参数:
    - speed: 200
    - offset: 80.0
  - 正在更新 motor_y 的参数:
    - speed: 220
[12:34:56.795] 动态配置更新完成。
[12:34:56.796] 轴偏移已更新: X=80.0, Y=75.0
[12:34:56.797] ===== 配置更新流程结束 =====
```

## 安全特性

### 1. 原子性保证
- 配置更新使用深拷贝机制，确保操作的原子性
- 如果更新过程中出现错误，原始配置保持不变
- 避免了部分更新导致的配置不一致状态

### 2. 线程安全
- 使用配置锁防止并发更新冲突
- CAM轴线程使用独立的配置副本，避免竞态条件
- 确保多线程环境下的配置一致性

## 注意事项

### 1. 配置文件格式
- 确保 `Config_Move.yaml` 文件格式正确
- 如果配置文件有语法错误，更新会失败并继续使用旧配置
- 原子性机制确保错误不会破坏现有配置

### 2. 参数范围
- 修改参数时注意合理的数值范围
- 速度参数不应超过硬件限制
- 位置参数应在有效行程范围内

### 3. 实时性
- 配置更新只在游戏周期结束后生效
- 如果需要立即生效，需要执行一次抓取操作

### 4. 备份建议
- 修改配置前建议备份原始配置文件
- 可以准备多个配置文件用于不同场景

## 测试验证

运行测试脚本验证功能：
```bash
python test_config_update.py
```

测试脚本会：
1. 加载原始配置
2. 创建修改后的测试配置
3. 测试配置更新功能
4. 验证更新结果
5. 检查保护参数未被修改

## 故障排除

### 1. 配置更新失败
- 检查配置文件路径是否正确
- 检查配置文件格式是否有误
- 查看日志输出中的错误信息

### 2. 参数未生效
- 确认已执行完整的抓取周期
- 检查修改的参数是否在支持更新的列表中
- 查看日志确认更新是否成功

### 3. 程序异常
- 如果配置更新导致程序异常，重启程序会恢复到文件中的配置
- 检查修改的参数值是否在合理范围内
