#被调用，用于给电机发送移动命令
import time
from Move_Utils import crc16, calculate_accel_time, estimate_motion_time, estimate_motion_time_cam, load_motor_config # Added load_motor_config
from Move_Utils import serial_lock as global_serial_lock, trace_lock
import threading # Make sure threading is imported

def send_modbus_command(ser, slave_addr, function_code, data, wait_time=0.02):
    """
    发送Modbus命令
    
    Args:
        ser: 串口对象
        slave_addr: 从站地址
        function_code: 功能码
        data: 数据
        wait_time: 发送后等待时间(秒)，默认0.02秒
    """
    # 清空接收缓冲区，确保读取到的是新的响应
    if ser.in_waiting > 0:
        ser.read(ser.in_waiting)
    request_core = bytes([slave_addr, function_code]) + data # Renamed
    request = request_core + crc16(request_core)
    # print(f"发送命令: 从站={slave_addr}, 功能码={function_code}, 数据长度={len(data)}")
    # print(f"命令内容: {' '.join([f'{b:02X}' for b in request])}")
    ser.write(request)
    time.sleep(wait_time)

def move_motor(ser, distance_mm: float, config, motor_key, serial_lock_obj=None): # Added serial_lock_obj
    """
    控制电机移动指定距离
    
    Args:
        ser: 串口对象
        distance_mm: 移动距离(mm)，正负表示方向
        config: 配置字典
        motor_key: 电机配置键名
        
    Returns:
        估算的运行时间(秒)
    """
    motor_config = config[motor_key]
    slave_addr = motor_config['slave_addr']
    pulse_per_rev = motor_config['pulse_per_rev']
    lead = motor_config['lead']
    speed = motor_config['speed']
    global_max_speed = config.get('global_settings', {}).get('max_speed_rpm', float('inf'))
    if speed > global_max_speed: speed = global_max_speed
    if distance_mm == 0: return 0.0
    revolutions = distance_mm / lead
    pulses = int(round(revolutions * pulse_per_rev))
    speed_positive = int(round(abs(speed)))

    # --- CAM轴双加速度 ---
    if motor_key == 'motor_cam':
        is_positive = distance_mm > 0
        accel_fast = motor_config.get('max_acceleration_fast', 0.5)
        accel_slow = motor_config.get('max_acceleration_slow', 0.5)
        if is_positive:
            accel_time = calculate_accel_time(lead, abs(speed), accel_fast)
            decel_time = calculate_accel_time(lead, abs(speed), accel_slow)
        else:
            accel_time = calculate_accel_time(lead, abs(speed), accel_slow)
            decel_time = calculate_accel_time(lead, abs(speed), accel_fast)
    else:
        max_acceleration = motor_config['max_acceleration']
        accel_time = calculate_accel_time(lead, abs(speed), max_acceleration)
        decel_time = accel_time

    accel_time_ms = max(int(accel_time * 1000), 100)
    accel_time_ms = min(accel_time_ms, 65535)
    decel_time_ms = max(int(decel_time * 1000), 100)
    decel_time_ms = min(decel_time_ms, 65535)

    if pulses < 0: pulses = (1 << 32) + pulses
    target_h = (pulses >> 16) & 0xFFFF
    target_l = pulses & 0xFFFF
    data_pos = bytes([
        0x00, 0x34, 0x00, 0x05, 0x0A,
        (accel_time_ms >> 8) & 0xFF, accel_time_ms & 0xFF,
        (decel_time_ms >> 8) & 0xFF, decel_time_ms & 0xFF,
        (speed_positive >> 8) & 0xFF, speed_positive & 0xFF,
        (target_h >> 8) & 0xFF, target_h & 0xFF,
        (target_l >> 8) & 0xFF, target_l & 0xFF
    ])
    data_trigger = bytes([0x00, 0x4E, 0x00, 0x01])

    current_lock = serial_lock_obj if serial_lock_obj else global_serial_lock

    trace_lock(f"Attempting to acquire for move_motor {motor_key}", current_lock)
    with current_lock:
        trace_lock(f"Acquired for move_motor {motor_key}", current_lock)
        try:
            send_modbus_command(ser, slave_addr, 0x10, data_pos)
            send_modbus_command(ser, slave_addr, 0x06, data_trigger)
        finally:
            trace_lock(f"Released for move_motor {motor_key}", current_lock)

    # 估算时间
    if motor_key == 'motor_cam':
        est_time = estimate_motion_time_cam(distance_mm, lead, speed, accel_fast, accel_slow)
    else:
        est_time = estimate_motion_time(distance_mm, lead, speed, max_acceleration)
    return est_time

def move_motor_with_speed(ser, distance_mm: float, config, motor_key, speed_rpm, serial_lock_obj=None): # Added serial_lock_obj
    """
    控制电机以指定速度移动指定距离
    
    Args:
        ser: 串口对象
        distance_mm: 移动距离(mm)，正负表示方向
        config: 配置字典
        motor_key: 电机配置键名
        speed_rpm: 指定的运行速度(RPM)
        
    Returns:
        估算的运行时间(秒)
    """
    motor_config = config[motor_key]
    slave_addr = motor_config['slave_addr']
    pulse_per_rev = motor_config['pulse_per_rev']
    lead = motor_config['lead']
    global_max_speed = config.get('global_settings', {}).get('max_speed_rpm', float('inf'))
    if speed_rpm > global_max_speed: speed_rpm = global_max_speed
    if speed_rpm < 1.0: speed_rpm = 1.0
    if distance_mm == 0: return 0.0
    revolutions = distance_mm / lead
    pulses = int(round(revolutions * pulse_per_rev))
    speed_positive = int(round(abs(speed_rpm)))

    # --- CAM轴双加速度 ---
    if motor_key == 'motor_cam':
        is_positive = distance_mm > 0
        accel_fast = motor_config.get('max_acceleration_fast', 0.5)
        accel_slow = motor_config.get('max_acceleration_slow', 0.5)
        if is_positive:
            accel_time = calculate_accel_time(lead, abs(speed_rpm), accel_fast)
            decel_time = calculate_accel_time(lead, abs(speed_rpm), accel_slow)
        else:
            accel_time = calculate_accel_time(lead, abs(speed_rpm), accel_slow)
            decel_time = calculate_accel_time(lead, abs(speed_rpm), accel_fast)
    else:
        max_acceleration = motor_config['max_acceleration']
        accel_time = calculate_accel_time(lead, abs(speed_rpm), max_acceleration)
        decel_time = accel_time

    accel_time_ms = max(int(accel_time * 1000), 100)
    accel_time_ms = min(accel_time_ms, 65535)
    decel_time_ms = max(int(decel_time * 1000), 100)
    decel_time_ms = min(decel_time_ms, 65535)

    if pulses < 0: pulses = (1 << 32) + pulses
    target_h = (pulses >> 16) & 0xFFFF
    target_l = pulses & 0xFFFF
    data_pos = bytes([
        0x00, 0x34, 0x00, 0x05, 0x0A,
        (accel_time_ms >> 8) & 0xFF, accel_time_ms & 0xFF,
        (decel_time_ms >> 8) & 0xFF, decel_time_ms & 0xFF,
        (speed_positive >> 8) & 0xFF, speed_positive & 0xFF,
        (target_h >> 8) & 0xFF, target_h & 0xFF,
        (target_l >> 8) & 0xFF, target_l & 0xFF
    ])
    data_trigger = bytes([0x00, 0x4E, 0x00, 0x01])

    current_lock = serial_lock_obj if serial_lock_obj else global_serial_lock

    trace_lock(f"Attempting to acquire for move_motor_with_speed {motor_key}", current_lock)
    with current_lock:
        trace_lock(f"Acquired for move_motor_with_speed {motor_key}", current_lock)
        try:
            send_modbus_command(ser, slave_addr, 0x10, data_pos)
            send_modbus_command(ser, slave_addr, 0x06, data_trigger)
        finally:
            trace_lock(f"Released for move_motor_with_speed {motor_key}", current_lock)

    # 估算时间
    if motor_key == 'motor_cam':
        est_time = estimate_motion_time_cam(distance_mm, lead, speed_rpm, accel_fast, accel_slow)
    else:
        est_time = estimate_motion_time(distance_mm, lead, speed_rpm, max_acceleration)
    return est_time

def read_motor_status(ser, slave_addr, serial_lock_obj=None): # Added serial_lock_obj
    """
    读取电机状态 (PA_004) - Move_commands version
    """
    read_data = bytes([0x00, 0x04, 0x00, 0x01])
    read_request_core = bytes([slave_addr, 0x03]) + read_data
    read_request = read_request_core + crc16(read_request_core)
    
    response_bytes = None
    current_lock = serial_lock_obj if serial_lock_obj else global_serial_lock

    trace_lock(f"Attempting to acquire for read_motor_status (cmd) Addr:{slave_addr}", current_lock)
    with current_lock:
        trace_lock(f"Acquired for read_motor_status (cmd) Addr:{slave_addr}", current_lock)
        try:
            if ser.in_waiting > 0:
                ser.read(ser.in_waiting)
            ser.write(read_request)
            time.sleep(0.02) 
            if ser.in_waiting > 0:
                response_bytes = ser.read(ser.in_waiting)
        finally:
            trace_lock(f"Released for read_motor_status (cmd) Addr:{slave_addr}", current_lock)
            
    if response_bytes:
        response = response_bytes
        start_idx = -1
        for i in range(len(response) - 4):
            if response[i] == slave_addr and response[i+1] == 0x03:
                start_idx = i
                break
        if start_idx >= 0 and start_idx + 4 < len(response): # Check bounds
            byte_count = response[start_idx + 2]
            if start_idx + 3 + byte_count <= len(response): # Check bounds for data
                status_value = (response[start_idx + 3] << 8) + response[start_idx + 4]
                return status_value
    return None

def read_motor_position(ser, slave_addr, serial_lock_obj=None):
    """
    读取电机当前位置 (PA_008)，返回pulse（int），失败返回None
    """
    read_data = bytes([0x00, 0x08, 0x00, 0x02])  # PA_008, 读取2个寄存器（4字节）
    read_request_core = bytes([slave_addr, 0x03]) + read_data
    read_request = read_request_core + crc16(read_request_core)

    response_bytes = None
    current_lock = serial_lock_obj if serial_lock_obj else global_serial_lock

    with current_lock:
        if ser.in_waiting > 0:
            ser.read(ser.in_waiting)
        ser.write(read_request)
        time.sleep(0.02)
        if ser.in_waiting > 0:
            response_bytes = ser.read(ser.in_waiting)
    if response_bytes:
        # 查找有效响应头
        start_idx = -1
        for i in range(len(response_bytes) - 6):
            if response_bytes[i] == slave_addr and response_bytes[i+1] == 0x03:
                start_idx = i
                break
        if start_idx >= 0 and start_idx + 6 < len(response_bytes):
            pos_high = (response_bytes[start_idx+3] << 8) + response_bytes[start_idx+4]
            pos_low  = (response_bytes[start_idx+5] << 8) + response_bytes[start_idx+6]
            position = (pos_high << 16) + pos_low
            # 处理负数（补码）
            if position & 0x80000000:
                position = position - (1 << 32)
            return position
    return None

def check_motor_fault_status(status):
    """
    统一判定电机故障状态。
    返回 (is_fault, fault_reason)
    """
    if status is None:
        return True, "读取状态失败"
    bit3_fault     = bool(status & 0x0008)  # 故障
    bit5_pos_limit = bool(status & 0x0020)  # 正软限位

    if bit3_fault:
        return True, "检测到故障（Bit3=1）"
    #if (not bit2_running) and (not bit0_in_position):
    #    return True, "电机已停止但未到位"
    #if not bit4_enabled:
    #    return True, "电机未使能（Bit4=0）"
    if bit5_pos_limit:
        return True, "检测到正软限位（Bit5=1）"
    return False, ""

def monitor_axis_status_with_distance(
    ser, motor_key, config,
    completion_event,
    serial_lock_obj,
    global_program_stop_event,
    timestamp_func=None,
    do1_slave_addr=None,   # 仅Z轴需要
    relay_control_func=None, # 仅Z轴需要
    initial_pulse=None,      # 新增参数
    target_distance_mm=None, # 新增参数
    axis_fault_info=None,         # 新增
    axis_fault_info_lock=None     # 新增
):
    """
    通用轴监控：交替读取PA_004/PA_008，持续输出实际移动距离和百分比。
    支持Z轴抱闸控制（通过do1_slave_addr和relay_control_func）。
    """
    motor_config = config.get(motor_key, {})
    slave_addr = motor_config.get('slave_addr')
    supports_read = motor_config.get('supports_status_read', False)
    stop_not_in_position_delay = config.get('global_settings', {}).get('stop_not_in_position_delay', 0.07)
    monitor_status_delay = config.get('global_settings', {}).get('Monitor_status_delay', 0.1)
    tolerance_xy = config.get('global_settings', {}).get('Monitor_tolerance_XY', 15.0)
    tolerance_z = config.get('global_settings', {}).get('Monitor_tolerance_Z', 5.0)
    tolerance_cam = config.get('global_settings', {}).get('Monitor_tolerance_cam', 10.0)
    if motor_key == 'motor_x' or motor_key == 'motor_y':
        monitor_tolerance = tolerance_xy
    elif motor_key == 'motor_z':
        monitor_tolerance = tolerance_z
    elif motor_key == 'motor_cam':
        monitor_tolerance = tolerance_cam
    else:
        monitor_tolerance = 15.0
    pulse_per_rev = motor_config.get('pulse_per_rev')
    lead = motor_config.get('lead')
    target_distance_mm = None

    if not all([slave_addr is not None, supports_read, pulse_per_rev, lead]):
        log_msg = f"{motor_key}: Monitoring disabled due to missing config or not supported."
        print(f"[{timestamp_func() if timestamp_func else ''}] {log_msg}")
        if completion_event: completion_event.set()
        return

    # 优先使用传入的 initial_pulse
    if initial_pulse is not None:
        use_initial_pulse = initial_pulse
    else:
        use_initial_pulse = read_motor_position(ser, slave_addr, serial_lock_obj)
    if use_initial_pulse is None:
        print(f"[{timestamp_func() if timestamp_func else ''}] {motor_key}: 读取初始位置失败，无法监控。")
        if completion_event: completion_event.set()
        return

    # 目标距离（mm），用于百分比显示
    # if hasattr(completion_event, "target_distance_mm"):
    #     target_distance_mm = completion_event.target_distance_mm
    # 直接用传入的 target_distance_mm

    try:
        stopped_time = None
        last_status = None
        last_pulse = use_initial_pulse
        read_pa008_next = True  # 交替读取
        motion_completed = False

        # 新增：累计反向位移变量
        reverse_move_sum = 0.0
        last_move_mm = 0.0

        while not completion_event.is_set() and not (global_program_stop_event and global_program_stop_event.is_set()):
            if read_pa008_next:
                curr_pulse = read_motor_position(ser, slave_addr, serial_lock_obj)
                if curr_pulse is not None:
                    last_pulse = curr_pulse
                    delta_pulse = curr_pulse - use_initial_pulse
                    move_mm = delta_pulse / pulse_per_rev * lead
                    msg = f"{motor_key}: 实际移动 {move_mm:.3f} mm"
                    if target_distance_mm:
                        percent = move_mm / target_distance_mm * 100
                        msg += f" ({percent:.1f}%)"
                    print(f"[{timestamp_func() if timestamp_func else ''}] {msg}")

                    # --- 新增：累计反向位移法判定运动方向错误 ---
                    if target_distance_mm is not None and abs(target_distance_mm) > 0.1:
                        delta_move = move_mm - last_move_mm
                        # 正向目标，累计负向位移
                        if target_distance_mm > 0:
                            if delta_move < 0:
                                reverse_move_sum += abs(delta_move)
                                if reverse_move_sum > monitor_tolerance:
                                    if axis_fault_info is not None and axis_fault_info_lock is not None:
                                        with axis_fault_info_lock:
                                            axis_fault_info[motor_key] = {
                                                'fault_type': 'hardware_fault',
                                                'fault_msg': f'累计反向位移超限（累计{reverse_move_sum:.3f}mm > 容差{monitor_tolerance}mm）'
                                            }
                                    # ...existing stop logic...
                                    if do1_slave_addr and relay_control_func:
                                        relay_control_func(ser, 0, 0, do1_slave_addr, serial_lock_obj)
                                    if global_program_stop_event: global_program_stop_event.set()
                                    completion_event.set()
                                    motion_completed = True
                                    break
                            else:
                                reverse_move_sum = 0.0
                        # 负向目标，累计正向位移
                        elif target_distance_mm < 0:
                            if delta_move > 0:
                                reverse_move_sum += abs(delta_move)
                                if reverse_move_sum > monitor_tolerance:
                                    if axis_fault_info is not None and axis_fault_info_lock is not None:
                                        with axis_fault_info_lock:
                                            axis_fault_info[motor_key] = {
                                                'fault_type': 'hardware_fault',
                                                'fault_msg': f'累计反向位移超限（累计{reverse_move_sum:.3f}mm > 容差{monitor_tolerance}mm）'
                                            }
                                    # ...existing stop logic...
                                    if do1_slave_addr and relay_control_func:
                                        relay_control_func(ser, 0, 0, do1_slave_addr, serial_lock_obj)
                                    if global_program_stop_event: global_program_stop_event.set()
                                    completion_event.set()
                                    motion_completed = True
                                    break
                            else:
                                reverse_move_sum = 0.0
                        last_move_mm = move_mm

                        # 单次方向错误
                        if target_distance_mm > 0 and move_mm < -monitor_tolerance:
                            if axis_fault_info is not None and axis_fault_info_lock is not None:
                                with axis_fault_info_lock:
                                    axis_fault_info[motor_key] = {
                                        'fault_type': 'hardware_fault',
                                        'fault_msg': f'运动方向错误，目标正向{target_distance_mm}mm，当前位置{move_mm:.3f}mm，判定为故障！'
                                    }
                            if do1_slave_addr and relay_control_func:
                                relay_control_func(ser, 0, 0, do1_slave_addr, serial_lock_obj)
                            if global_program_stop_event: global_program_stop_event.set()
                            completion_event.set()
                            motion_completed = True
                            break
                        if target_distance_mm < 0 and move_mm > monitor_tolerance:
                            if axis_fault_info is not None and axis_fault_info_lock is not None:
                                with axis_fault_info_lock:
                                    axis_fault_info[motor_key] = {
                                        'fault_type': 'hardware_fault',
                                        'fault_msg': f'运动方向错误，目标负向{target_distance_mm}mm，当前位置{move_mm:.3f}mm，判定为故障！'
                                    }
                            if do1_slave_addr and relay_control_func:
                                relay_control_func(ser, 0, 0, do1_slave_addr, serial_lock_obj)
                            if global_program_stop_event: global_program_stop_event.set()
                            completion_event.set()
                            motion_completed = True
                            break
                        # 正向超差
                        if target_distance_mm > 0 and move_mm > target_distance_mm + monitor_tolerance:
                            if axis_fault_info is not None and axis_fault_info_lock is not None:
                                with axis_fault_info_lock:
                                    axis_fault_info[motor_key] = {
                                        'fault_type': 'hardware_fault',
                                        'fault_msg': f'运动中超出正向容差，目标{target_distance_mm}mm，当前位置{move_mm:.3f}mm，容差{monitor_tolerance}mm，判定为故障！'
                                    }
                            if do1_slave_addr and relay_control_func:
                                relay_control_func(ser, 0, 0, do1_slave_addr, serial_lock_obj)
                            if global_program_stop_event: global_program_stop_event.set()
                            completion_event.set()
                            motion_completed = True
                            break
                        # 负向超差
                        if target_distance_mm < 0 and move_mm < target_distance_mm - monitor_tolerance:
                            if axis_fault_info is not None and axis_fault_info_lock is not None:
                                with axis_fault_info_lock:
                                    axis_fault_info[motor_key] = {
                                        'fault_type': 'hardware_fault',
                                        'fault_msg': f'运动中超出负向容差，目标{target_distance_mm}mm，当前位置{move_mm:.3f}mm，容差{monitor_tolerance}mm，判定为故障！'
                                    }
                            if do1_slave_addr and relay_control_func:
                                relay_control_func(ser, 0, 0, do1_slave_addr, serial_lock_obj)
                            if global_program_stop_event: global_program_stop_event.set()
                            completion_event.set()
                            motion_completed = True
                            break
                read_pa008_next = False
            else:
                status = read_motor_status(ser, slave_addr, serial_lock_obj)
                last_status = status

                if status is None:
                    if axis_fault_info is not None and axis_fault_info_lock is not None:
                        with axis_fault_info_lock:
                            axis_fault_info[motor_key] = {
                                'fault_type': 'hardware_fault',
                                'fault_msg': '读取状态失败'
                            }
                    if do1_slave_addr and relay_control_func:
                        relay_control_func(ser, 0, 0, do1_slave_addr, serial_lock_obj)
                    if global_program_stop_event: global_program_stop_event.set()
                    completion_event.set()
                    motion_completed = True
                    break

                is_fault, reason = check_motor_fault_status(status)
                bit0_in_position = bool(status & 0x0001)
                bit2_running     = bool(status & 0x0004)
                bit4_enabled     = bool(status & 0x0010)
                print(f"[{timestamp_func() if timestamp_func else ''}] {motor_key}: 到位 Bit0={bit0_in_position}，运行 Bit2={bit2_running}，使能 Bit4={bit4_enabled}")

                if is_fault:
                    if axis_fault_info is not None and axis_fault_info_lock is not None:
                        with axis_fault_info_lock:
                            axis_fault_info[motor_key] = {
                                'fault_type': 'hardware_fault',
                                'fault_msg': reason
                            }
                    if do1_slave_addr and relay_control_func:
                        relay_control_func(ser, 0, 0, do1_slave_addr, serial_lock_obj)
                    if global_program_stop_event: global_program_stop_event.set()
                    completion_event.set()
                    motion_completed = True
                    break

                if not bit2_running:
                    if bit0_in_position:
                        print(f"[{timestamp_func() if timestamp_func else ''}] {motor_key}: 电机到位，运动完成。")
                        if do1_slave_addr and relay_control_func:
                            relay_control_func(ser, 0, 0, do1_slave_addr, serial_lock_obj)
                        completion_event.set()
                        motion_completed = True
                        break
                    if stopped_time is None:
                        stopped_time = time.time()
                    if time.time() - stopped_time >= stop_not_in_position_delay:
                        if axis_fault_info is not None and axis_fault_info_lock is not None:
                            with axis_fault_info_lock:
                                axis_fault_info[motor_key] = {
                                    'fault_type': 'hardware_fault',
                                    'fault_msg': '电机停止但未到位'
                                }
                        if do1_slave_addr and relay_control_func:
                            relay_control_func(ser, 0, 0, do1_slave_addr, serial_lock_obj)
                        if global_program_stop_event: global_program_stop_event.set()
                        completion_event.set()
                        motion_completed = True
                        break
                else:
                    stopped_time = None
                read_pa008_next = True

            if global_program_stop_event and global_program_stop_event.wait(timeout=0.02):
                break
            elif not global_program_stop_event:
                time.sleep(0.02)

        # 运动结束后，等待Monitor_status_delay再采样一次
        if motion_completed:
            print(f"[{timestamp_func() if timestamp_func else ''}] {motor_key}: 运动完成，等待 {monitor_status_delay:.3f}s 后采样位置以消除反馈延迟影响...")
            time.sleep(monitor_status_delay)
            curr_pulse = read_motor_position(ser, slave_addr, serial_lock_obj)
            if curr_pulse is not None:
                delta_pulse = curr_pulse - use_initial_pulse
                move_mm = delta_pulse / pulse_per_rev * lead
                msg = f"{motor_key}: 实际移动 {move_mm:.3f} mm"
                if target_distance_mm:
                    percent = move_mm / target_distance_mm * 100
                    msg += f" ({percent:.1f}%)"
                print(f"[{timestamp_func() if timestamp_func else ''}] {msg}（延迟采样）")

                # 延迟采样超差判定
                if target_distance_mm is not None and abs(target_distance_mm) > 0.1:
                    if abs(move_mm - target_distance_mm) > monitor_tolerance:
                        if axis_fault_info is not None and axis_fault_info_lock is not None:
                            with axis_fault_info_lock:
                                axis_fault_info[motor_key] = {
                                    'fault_type': 'hardware_fault',
                                    'fault_msg': f'延迟采样偏差超出容差，目标{target_distance_mm}mm，当前位置{move_mm:.3f}mm，容差{monitor_tolerance}mm'
                                }
                        if do1_slave_addr and relay_control_func:
                            relay_control_func(ser, 0, 0, do1_slave_addr, serial_lock_obj)
                        if global_program_stop_event: global_program_stop_event.set()
    except Exception as e:
        if axis_fault_info is not None and axis_fault_info_lock is not None:
            with axis_fault_info_lock:
                axis_fault_info[motor_key] = {
                    'fault_type': 'monitor_exception',
                    'fault_msg': f'监控线程异常: {e}'
                }
        print(f"[{timestamp_func() if timestamp_func else ''}] {motor_key} (Addr: {slave_addr}) monitoring encountered an error: {e}")
        import traceback
        traceback.print_exc()
        if do1_slave_addr and relay_control_func:
            try:
                relay_control_func(ser, 0, 0, do1_slave_addr, serial_lock_obj)
            except Exception as e2:
                print(f"[{timestamp_func() if timestamp_func else ''}] {motor_key}: Failed to activate brake during error handling: {e2}")
        if global_program_stop_event: global_program_stop_event.set()
        monitor_exception = True
    else:
        monitor_exception = False
    finally:
        if global_program_stop_event and global_program_stop_event.is_set():
            exit_reason = "global stop"
        elif monitor_exception:
            exit_reason = "error"
        else:
            exit_reason = "normal"
        print(f"[{timestamp_func() if timestamp_func else ''}] {motor_key} (Addr: {slave_addr}): Monitoring loop exited ({exit_reason}).")
        if completion_event and not completion_event.is_set():
            completion_event.set()
