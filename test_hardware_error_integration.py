#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
硬件错误集成测试程序
测试完整的错误处理流程：Move_commands -> Move_Control -> Move_main -> move_network -> 客户端
"""

import time
import threading
import json
import socket
import datetime
import copy
from unittest.mock import Mock, patch
import sys
import os

# 添加当前目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

# 导入需要测试的模块
from Move_Utils import load_motor_config
from Move_Main import process_movement
import Move_commands

def get_timestamp():
    """获取当前时间戳字符串，格式为HH:MM:SS.mmm"""
    now = datetime.datetime.now()
    return now.strftime("%H:%M:%S.") + f"{now.microsecond//1000:03d}"

class IntegratedHardwareErrorTester:
    """集成硬件错误测试器"""
    
    def __init__(self):
        self.test_results = {}
        self.mock_serial = None
        self.error_injection_active = False
        self.current_error_type = None
        self.original_functions = {}
        self.network_events = []
        
    def setup_mock_serial(self):
        """设置模拟串口"""
        self.mock_serial = Mock()
        self.mock_serial.is_open = True
        self.mock_serial.write = Mock()
        self.mock_serial.read = Mock(return_value=b'\x01\x06\x00\x4E\x00\x00\x00\x00')
        self.mock_serial.close = Mock()
        self.mock_serial.in_waiting = 8
        self.mock_serial.timeout = 1.0
        
    def inject_hardware_error(self, error_type):
        """注入硬件错误"""
        self.error_injection_active = True
        self.current_error_type = error_type
        
        if error_type == 1:  # 累计反向位移超限
            self.inject_cumulative_reverse_error()
        elif error_type == 2:  # 运动方向错误
            self.inject_direction_error()
        elif error_type == 3:  # 运动中超出容差
            self.inject_tolerance_error()
        elif error_type == 4:  # 延迟采样偏差超限
            self.inject_sampling_error()
        elif error_type == 5:  # 读取状态失败
            self.inject_status_read_error()
        elif error_type == 6:  # 电机故障状态
            self.inject_motor_fault_error()
            
    def inject_cumulative_reverse_error(self):
        """注入累计反向位移错误"""
        original_read_position = Move_commands.read_motor_position
        
        def mock_read_position(ser, slave_addr, serial_lock):
            if self.error_injection_active and self.current_error_type == 1:
                counter = getattr(mock_read_position, 'counter', 0)
                mock_read_position.counter = counter + 1
                
                if counter < 3:
                    return 0  # 初始位置
                else:
                    # 返回负向位移，累计超过容差
                    return -1500 * (counter - 2)  # 负向脉冲
            return original_read_position(ser, slave_addr, serial_lock)
        
        Move_commands.read_motor_position = mock_read_position
        self.original_functions['read_motor_position'] = original_read_position
        
    def inject_direction_error(self):
        """注入运动方向错误"""
        original_read_position = Move_commands.read_motor_position
        
        def mock_read_position(ser, slave_addr, serial_lock):
            if self.error_injection_active and self.current_error_type == 2:
                return -5000  # 大负值，方向错误
            return original_read_position(ser, slave_addr, serial_lock)
        
        Move_commands.read_motor_position = mock_read_position
        self.original_functions['read_motor_position'] = original_read_position
        
    def inject_tolerance_error(self):
        """注入超出容差错误"""
        original_read_position = Move_commands.read_motor_position
        
        def mock_read_position(ser, slave_addr, serial_lock):
            if self.error_injection_active and self.current_error_type == 3:
                return 12000  # 超出目标+容差的位置
            return original_read_position(ser, slave_addr, serial_lock)
        
        Move_commands.read_motor_position = mock_read_position
        self.original_functions['read_motor_position'] = original_read_position
        
    def inject_sampling_error(self):
        """注入延迟采样错误"""
        original_read_position = Move_commands.read_motor_position
        
        def mock_read_position(ser, slave_addr, serial_lock):
            if self.error_injection_active and self.current_error_type == 4:
                counter = getattr(mock_read_position, 'counter', 0)
                mock_read_position.counter = counter + 1
                
                if counter < 10:
                    return 10000  # 运动过程中正常
                else:
                    return 13000  # 延迟采样时偏差过大
            return original_read_position(ser, slave_addr, serial_lock)
        
        Move_commands.read_motor_position = mock_read_position
        self.original_functions['read_motor_position'] = original_read_position
        
    def inject_status_read_error(self):
        """注入状态读取错误"""
        original_read_status = Move_commands.read_motor_status
        
        def mock_read_status(ser, slave_addr, serial_lock):
            if self.error_injection_active and self.current_error_type == 5:
                return None  # 读取失败
            return original_read_status(ser, slave_addr, serial_lock)
        
        Move_commands.read_motor_status = mock_read_status
        self.original_functions['read_motor_status'] = original_read_status
        
    def inject_motor_fault_error(self):
        """注入电机故障错误"""
        original_read_status = Move_commands.read_motor_status
        
        def mock_read_status(ser, slave_addr, serial_lock):
            if self.error_injection_active and self.current_error_type == 6:
                return 0x0008  # Bit3故障位
            return original_read_status(ser, slave_addr, serial_lock)
        
        Move_commands.read_motor_status = mock_read_status
        self.original_functions['read_motor_status'] = original_read_status
        
    def create_mock_event_socket(self):
        """创建模拟事件套接字"""
        mock_socket = Mock()
        
        def mock_sendall(data):
            try:
                message = data.decode('utf-8').strip()
                event = json.loads(message)
                self.network_events.append(event)
                print(f"[{get_timestamp()}] 网络事件已发送: {event.get('event_name', 'unknown')}")
            except Exception as e:
                print(f"[{get_timestamp()}] 解析网络事件失败: {e}")
                
        mock_socket.sendall = mock_sendall
        return mock_socket
        
    def test_integrated_error_flow(self, error_type, error_name):
        """测试集成错误流程"""
        print(f"\n{'='*70}")
        print(f"[{get_timestamp()}] 集成测试错误类型 {error_type}: {error_name}")
        print(f"{'='*70}")
        
        test_result = {
            'error_type': error_type,
            'error_name': error_name,
            'error_detected_in_commands': False,
            'emergency_stop_sent': False,
            'error_returned_to_main': False,
            'network_event_sent': False,
            'test_start_time': time.time(),
            'test_duration': 0,
            'details': {},
            'network_events': []
        }
        
        try:
            # 清空网络事件记录
            self.network_events.clear()

            # 重置全局停止事件
            import Move_Main
            if hasattr(Move_Main, 'global_program_stop_event'):
                Move_Main.global_program_stop_event.clear()
                print(f"[{get_timestamp()}] 已重置全局停止事件")

            # 注入错误
            self.inject_hardware_error(error_type)
            
            # 加载配置
            config = load_motor_config()
            if not config:
                test_result['details']['error'] = '配置加载失败'
                return test_result
                
            # 修改配置以适应测试
            config = self.modify_config_for_test(config)
            
            # 创建模拟事件套接字
            mock_event_socket = self.create_mock_event_socket()
            
            # 模拟全局变量
            import Move_Main
            original_serial = getattr(Move_Main, 'global_serial', None)
            original_lock = getattr(Move_Main, 'serial_lock', None)
            
            Move_Main.global_serial = self.mock_serial
            Move_Main.serial_lock = threading.Lock()
            
            # 调用process_movement测试完整流程
            print(f"[{get_timestamp()}] 调用process_movement测试完整错误处理流程...")

            # 修改当前位置，确保有实际移动距离
            import Move_Main
            original_positions = Move_Main.current_positions.copy()
            Move_Main.current_positions = {'X': 0.0, 'Y': 0.0, 'Z': 0.0, 'CAM': 0.0}

            result = process_movement(
                target_x=100.0,  # 100mm移动，确保超过偏移量
                target_y=100.0,
                target_z=10.0,
                target_cam=10.0,
                do2_val=2,
                config_object=config,
                current_main_lock=Move_Main.serial_lock,
                current_serial_port=self.mock_serial
            )

            # 恢复原始位置
            Move_Main.current_positions = original_positions
            
            # 分析结果
            if isinstance(result, dict) and result.get('error_type') == 'hardware_error':
                test_result['error_detected_in_commands'] = True
                test_result['emergency_stop_sent'] = True
                test_result['error_returned_to_main'] = True
                test_result['details']['faults'] = result.get('faults', [])
                print(f"[{get_timestamp()}] ✓ 硬件错误正确检测并返回到Main层")
                print(f"[{get_timestamp()}] 故障信息: {result.get('faults', [])}")
            else:
                test_result['details']['result'] = result
                print(f"[{get_timestamp()}] ✗ 未检测到预期的硬件错误，返回: {result}")
                
            # 检查网络事件
            if self.network_events:
                test_result['network_event_sent'] = True
                test_result['network_events'] = self.network_events.copy()
                print(f"[{get_timestamp()}] ✓ 网络事件已发送，共 {len(self.network_events)} 个事件")
            else:
                print(f"[{get_timestamp()}] ✗ 未发送网络事件")
                
            # 恢复全局变量
            if original_serial is not None:
                Move_Main.global_serial = original_serial
            if original_lock is not None:
                Move_Main.serial_lock = original_lock
                
        except Exception as e:
            test_result['details']['exception'] = str(e)
            print(f"[{get_timestamp()}] ✗ 测试过程中发生异常: {e}")
            import traceback
            traceback.print_exc()
            
        finally:
            # 停用错误注入
            self.deactivate_error()
            test_result['test_duration'] = time.time() - test_result['test_start_time']
            
        return test_result
        
    def modify_config_for_test(self, config):
        """修改配置以适应测试"""
        test_config = copy.deepcopy(config)
        
        if 'global_settings' not in test_config:
            test_config['global_settings'] = {}
            
        test_config['global_settings']['Monitor_tolerance'] = 1.0
        test_config['global_settings']['Monitor_status_delay'] = 0.1
        
        return test_config
        
    def deactivate_error(self):
        """停用错误注入"""
        self.error_injection_active = False
        self.current_error_type = None
        
        # 恢复原始函数
        for func_name, original_func in self.original_functions.items():
            setattr(Move_commands, func_name, original_func)
        self.original_functions.clear()
        
    def run_integration_tests(self):
        """运行集成测试"""
        error_types = [
            (1, "累计反向位移超限"),
            (2, "运动方向错误"),
            (3, "运动中超出容差"),
            (4, "延迟采样偏差超限"),
            (5, "读取状态失败"),
            (6, "电机故障状态(Bit3)")
        ]
        
        print(f"[{get_timestamp()}] 开始硬件错误集成测试")
        print(f"总共测试 {len(error_types)} 种错误类型")
        
        for error_type, error_name in error_types:
            test_result = self.test_integrated_error_flow(error_type, error_name)
            self.test_results[error_type] = test_result
            time.sleep(1)  # 测试间隔
            
        return self.test_results
        
    def generate_integration_report(self):
        """生成集成测试报告"""
        print(f"\n{'='*80}")
        print(f"硬件错误集成测试报告")
        print(f"测试时间: {datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        print(f"{'='*80}")
        
        total_tests = len(self.test_results)
        full_success = 0
        
        for error_type, result in self.test_results.items():
            # 判断是否完全成功（所有环节都正常）
            is_full_success = (
                result['error_detected_in_commands'] and
                result['emergency_stop_sent'] and
                result['error_returned_to_main']
            )
            
            if is_full_success:
                full_success += 1
                
            status = "✓ 完全通过" if is_full_success else "⚠ 部分通过"
            
            print(f"\n错误类型 {error_type}: {result['error_name']}")
            print(f"  状态: {status}")
            print(f"  Commands层检测: {'✓' if result['error_detected_in_commands'] else '✗'}")
            print(f"  Control层急停: {'✓' if result['emergency_stop_sent'] else '✗'}")
            print(f"  Main层接收: {'✓' if result['error_returned_to_main'] else '✗'}")
            print(f"  网络事件发送: {'✓' if result['network_event_sent'] else '✗'}")
            print(f"  测试耗时: {result['test_duration']:.2f}秒")
            
            if result['network_events']:
                print(f"  网络事件数量: {len(result['network_events'])}")
                
        print(f"\n{'='*80}")
        print(f"集成测试总结:")
        print(f"  总测试数: {total_tests}")
        print(f"  完全通过: {full_success}")
        print(f"  部分通过: {total_tests - full_success}")
        print(f"  完全通过率: {full_success/total_tests*100:.1f}%")
        print(f"{'='*80}")
        
        return self.test_results

def main():
    """主函数"""
    tester = IntegratedHardwareErrorTester()
    
    try:
        # 设置模拟串口
        tester.setup_mock_serial()
        
        # 运行集成测试
        results = tester.run_integration_tests()
        
        # 生成测试报告
        tester.generate_integration_report()
        
    except KeyboardInterrupt:
        print(f"\n[{get_timestamp()}] 用户中断测试")
    except Exception as e:
        print(f"[{get_timestamp()}] 测试过程中发生错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
