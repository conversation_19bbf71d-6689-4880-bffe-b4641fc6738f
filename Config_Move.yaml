# Serial Port Configuration
serial:
  port: "COM1"    # 设置串口号（如 COM3、/dev/ttyUSB0）
  baudrate: 19200  # 设置波特率
  timeout: 0.012      # 超时时间（秒）

# 网络服务配置（新增）
motormove_service:
  host: 'localhost'     # 服务器主机名或IP地址
  port: 5556           # Move_Main.py 作为服务端的端口号

# 检测服务连接配置（新增）
detection_service:
  host: 'localhost'     # 检测服务的主机名或IP地址
  port: 5555           # 检测服务的端口号

# 摄像头配置
camera:
  width: 2560
  height: 1440
  # display_scale: 2  # Windows显示缩放比例，以防超出屏幕，服务模式下用不到 (如1.25 代表 125%)

# 全局速度限制
global_settings:
  max_speed_rpm: 600  # 最大速度限制（RPM），不能超过700，否则怕z轴往下掉 [动态加载]
  motion_timeout: 180  # 运动超时时间（秒） [动态加载]
  stop_not_in_position_delay: 0.15   # 电机停止后持续监控时间，如果还未到位，就判故障（秒）
  Monitor_status_delay: 0.1 #电机到位后继续采样的时间
  Monitor_tolerance_XY: 1.0 #电机采样容差，否则判定为故障（mm）
  Monitor_tolerance_Z: 1.0 #电机采样容差，否则判定为故障（mm）
  Monitor_tolerance_cam: 1.0 #电机采样容差，否则判定为故障（mm）

# 鱼缸尺寸
fish_tank:
  tank_x: 685.0      # 鱼缸长度（mm） [动态加载]
  tank_y: 685.0       # 鱼缸宽度（mm） [动态加载]
  tank_z: 300.0      # 鱼缸高度（mm） [动态加载]

# 展示位置
display_pos:
  dis_pos_x: 600.0      # 展示位置x（mm） [动态加载]————
  dis_pos_y: 342.0       # 展示位置y（mm） [动态加载]
  dis_pos_z: 0.0      # 展示位置z（mm） [动态加载]
  dis_pos_time: 1.0      # 展示位置时间间隔（秒） [动态加载]

# 抓取动作设置
catch_settings:
  class_z_offsets:     # 各类物体抓取动作z轴偏移（mm），就是比最低点略高多少，按类别ID设置 [动态加载]
    '0': 5.0           # crab 螃蟹————
    '1': 5.0           # lobster 龙虾
    '2': 5.0           # shell 贝壳
  catch_time: 0.5      # 抓取动作等待时间（秒） [动态加载]

# 临时判断高度
temp_height:
  temp_height_z: 150.0      # 临时识别物体的高度z，从最高点往下算（mm）最好150。 [动态加载] ————
  temp_height_time: 0.05      # 还是要等待大于0.05秒，否则来不及读取抓取数据。 [动态加载]

# 摄像头展示动作配置
camera_showcase_settings:
  delay_before_move: 0.2    # 抓到物品后，等待多久再移动CAM轴 (秒) [动态加载]
  dwell_time_at_showcase: 1.5 # CAM轴移动到展示位后停留多久 (秒) [动态加载]————

# 物体投放设置
release_settings:
  release_height_z: 280.0     # 投放高度，从最高点往下算（mm） [动态加载]————
  release_time: 0.2        # 释放后等待时间（秒） [动态加载]
  safe_margin: 150.0        # 距离池边的安全距离（mm） [动态加载]
  rough_grid: 100.0        # 粗网格大小（mm） [动态加载]
  fine_grid: 10.0          # 细网格大小（mm） [动态加载]

# 回零参数配置（适用于所有轴）
homing:
  mode: 17            # 回零方式 (17=负限位模式, 18=正限位模式, 24=正向原点模式, 29=反向原点模式, 35=设置当前位置为原点) [动态加载]
  speed: 40           # 回零速度 (RPM) [动态加载]
  creep_speed: 8     # 回零爬行速度 (RPM) [动态加载]
  accel_time: 300     # 回零加减速时间 (ms) [动态加载]
  offset: 0           # 原点偏移值 [动态加载]
  max_wait_time: 180  # 回零超时时间（秒） [动态加载]

# X-axis Motor Parameters
motor_x:
  slave_addr: 3   # 站号
  pulse_per_rev: 36000  # 电机每转脉冲数
  lead: 105.0       # 滑台导程（mm/转）
  speed: 250        # 运行速度（RPM），工作速度250 [动态加载]
  max_acceleration: 0.6  # 最大加速度（米每平方秒） [动态加载]
  max_distance: 550.0  # 最大行程（毫米） [动态加载]
  offset: 70.0      # X轴初始偏移（毫米）,并且X方向两侧都要留出这个空挡，以防爪子碰撞 [动态加载]
  supports_status_read: true   # New
  status_running_mask: 0x0004  # New (bit 2: 1=运行中)
  status_fault_mask: 0x0008    # New (bit 3: 1=故障)

# Y-axis Motor Parameters
motor_y:
  slave_addr: 2   # 站号
  pulse_per_rev: 36000  # 电机每转脉冲数
  lead: 75.0       # 滑台导程（mm/转）
  speed: 350        # 运行速度（RPM），工作速度350 [动态加载]
  max_acceleration: 0.5  # 最大加速度（米每平方秒） [动态加载]
  max_distance: 545.0  # 最大行程（毫米） [动态加载]
  offset: 70.0      # Y轴初始偏移（毫米）,并且Y方向两侧都要留出这个空挡，以防爪子碰撞 [动态加载]
  supports_status_read: true   # New
  status_running_mask: 0x0004  # New
  status_fault_mask: 0x0008    # New

# Z-axis Motor Parameters
motor_z:
  slave_addr: 4   # 站号
  pulse_per_rev: 18000  # 电机每转脉冲数
  lead: 21.0       # 滑台导程（mm/转）
  speed: 400        # 工作速度400（RPM），速度不能超过600，否则z轴往下掉 [动态加载]
  max_acceleration: 0.4  # 最大加速度（米每平方秒） [动态加载]
  max_distance: 350.0  # 最大行程（毫米） [动态加载]
  supports_status_read: true   # New
  status_running_mask: 0x0004  # New
  status_fault_mask: 0x0008    # New
  sync_speed_compensation_factor: 1.2  # 新增：Z轴同步速度补偿系数

# CAM-axis Motor Parameters
motor_cam:
  slave_addr: 1   # 站号
  pulse_per_rev: 18000  # 电机每转脉冲数
  lead: 75.0       # 滑台导程（mm/转）
  speed: 400        # 工作速度400（RPM） [动态加载]
  max_acceleration_fast: 1.5  # 正向移动时加速和负向移动时减速用的最大加速度（米每平方秒） [动态加载]
  max_acceleration_slow: 1.1  # 正向移动减速时和负向移动加速时最大加速度（米每平方秒） [动态加载]
  max_distance: 400.0  # 最大行程（毫米） [动态加载]
  supports_status_read: true   # New
  status_running_mask: 0x0004  # New
  status_fault_mask: 0x0008    # New

# DO1 Output Interface (由华控继电器控制)抱闸
do1:
  slave_addr: 5   # 华控继电器站号
  pre_z_delay: 0.04  # Z轴移动前打开DO1的等待时间（秒）
  post_z_delay: 0.000  # Z轴移动后关闭DO1的等待时间（秒）

# DO2 Output Interface (由华控继电器控制)，气动爪
do2:
  slave_addr: 5   # 华控继电器站号

# Discrete Input Interface (由华控数字量采集模块控制)，压感开关，检测是否夹到物体
X1:
  slave_addr: 6   # 华控数字量输入站号